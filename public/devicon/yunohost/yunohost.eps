%!PS-Adobe-3.0 EPSF-3.0
%%Creator: cairo 1.15.10 (http://cairographics.org)
%%CreationDate: Wed Jul  4 23:03:21 2018
%%Pages: 1
%%DocumentData: Clean7Bit
%%LanguageLevel: 2
%%BoundingBox: 0 0 96 96
%%EndComments
%%BeginProlog
50 dict begin
/q { gsave } bind def
/Q { grestore } bind def
/cm { 6 array astore concat } bind def
/w { setlinewidth } bind def
/J { setlinecap } bind def
/j { setlinejoin } bind def
/M { setmiterlimit } bind def
/d { setdash } bind def
/m { moveto } bind def
/l { lineto } bind def
/c { curveto } bind def
/h { closepath } bind def
/re { exch dup neg 3 1 roll 5 3 roll moveto 0 rlineto
      0 exch rlineto 0 rlineto closepath } bind def
/S { stroke } bind def
/f { fill } bind def
/f* { eofill } bind def
/n { newpath } bind def
/W { clip } bind def
/W* { eoclip } bind def
/BT { } bind def
/ET { } bind def
/BDC { mark 3 1 roll /BDC pdfmark } bind def
/EMC { mark /EMC pdfmark } bind def
/cairo_store_point { /cairo_point_y exch def /cairo_point_x exch def } def
/Tj { show currentpoint cairo_store_point } bind def
/TJ {
  {
    dup
    type /stringtype eq
    { show } { -0.001 mul 0 cairo_font_matrix dtransform rmoveto } ifelse
  } forall
  currentpoint cairo_store_point
} bind def
/cairo_selectfont { cairo_font_matrix aload pop pop pop 0 0 6 array astore
    cairo_font exch selectfont cairo_point_x cairo_point_y moveto } bind def
/Tf { pop /cairo_font exch def /cairo_font_matrix where
      { pop cairo_selectfont } if } bind def
/Td { matrix translate cairo_font_matrix matrix concatmatrix dup
      /cairo_font_matrix exch def dup 4 get exch 5 get cairo_store_point
      /cairo_font where { pop cairo_selectfont } if } bind def
/Tm { 2 copy 8 2 roll 6 array astore /cairo_font_matrix exch def
      cairo_store_point /cairo_font where { pop cairo_selectfont } if } bind def
/g { setgray } bind def
/rg { setrgbcolor } bind def
/d1 { setcachedevice } bind def
/cairo_data_source {
  CairoDataIndex CairoData length lt
    { CairoData CairoDataIndex get /CairoDataIndex CairoDataIndex 1 add def }
    { () } ifelse
} def
/cairo_flush_ascii85_file { cairo_ascii85_file status { cairo_ascii85_file flushfile } if } def
/cairo_image { image cairo_flush_ascii85_file } def
/cairo_imagemask { imagemask cairo_flush_ascii85_file } def
%%EndProlog
%%BeginSetup
%%EndSetup
%%Page: 1 1
%%BeginPageSetup
%%PageBoundingBox: 0 0 96 96
%%EndPageSetup
q 0 0 96 96 rectclip
1 0 0 -1 0 96 cm q
0 g
7.5 0 m 88.5 0 l 92.656 0 96 3.344 96 7.5 c 96 88.5 l 96 92.656 92.656 
96 88.5 96 c 7.5 96 l 3.344 96 0 92.656 0 88.5 c 0 7.5 l 0 3.344 3.344 0
 7.5 0 c h
7.5 0 m f
1 g
12.418 85.824 m 11.578 85.258 10.445 83.953 9.902 82.926 c 8.852 80.934
 6.805 78.762 4.719 77.422 c 3.281 76.496 2.492 73.727 3.367 72.672 c 3.656
 72.32 4.895 71.766 6.113 71.438 c 7.332 71.109 8.531 70.66 8.777 70.441
 c 9.023 70.219 9.246 68.617 9.273 66.879 c 9.441 56.254 9.527 55.992 12.777
 55.992 c 16.633 55.992 19.242 60.602 17.973 65.172 c 17.074 68.41 17.254
 69.109 18.984 69.102 c 21.625 69.09 21.766 68.785 21.887 62.961 c 22.066
 54.172 23.988 51.379 28.41 53.484 c 31.211 54.82 31.625 58.445 29.707 64.895
 c 29.082 67 30.324 75.738 31.625 78.371 c 32.359 79.855 31.574 81.941 30.203
 82.137 c 29.582 82.227 28.562 81.793 27.719 81.086 c 26.93 80.422 26.121
 79.879 25.918 79.879 c 25.715 79.879 24.965 79.035 24.25 78.008 c 23.008
 76.211 21.59 75.605 20.973 76.602 c 20.812 76.859 19.727 77.312 18.559 
77.605 c 16.516 78.121 16.445 78.195 16.707 79.594 c 17.477 83.75 17.484
 85.41 16.738 86.152 c 15.723 87.168 14.254 87.059 12.418 85.824 c h
59.867 79.32 m 58.855 78.734 56.875 76.527 56 75.012 c 55.688 74.469 55.422
 73.012 55.414 71.77 c 55.391 68.453 56.781 66.367 57.504 68.637 c 58.188
 70.793 60.723 71.762 61.918 70.32 c 62.883 69.156 61.27 66.441 57.273 62.496
 c 53.68 58.953 53.523 58.711 53.523 56.672 c 53.523 53.504 54.43 52.191
 57.68 50.637 c 60.383 49.344 60.617 49.309 62.523 49.922 c 65.121 50.754
 68.168 53.688 67.961 55.156 c 67.832 56.07 67.531 56.227 65.934 56.227 
c 64.902 56.227 63.797 56.398 63.477 56.605 c 62.52 57.227 62.809 58.641
 64.062 59.465 c 65.215 60.219 65.23 60.211 65.23 58.902 c 65.23 57.613 
65.355 57.547 69.328 56.668 c 73.43 55.762 l 73.762 52.074 l 74.324 45.875
 76.117 44.648 79.656 48.051 c 80.738 49.09 81.656 50.195 81.695 50.508 
c 81.734 50.82 81.789 51.391 81.812 51.777 c 81.852 52.359 82.566 52.453
 85.898 52.32 c 89.789 52.164 89.992 52.207 91.398 53.488 c 92.402 54.406
 92.895 55.324 92.977 56.426 c 93.078 57.785 92.859 58.195 91.539 59.102
 c 90.68 59.688 87.887 60.836 85.332 61.648 c 81.664 62.82 80.688 63.312
 80.688 63.996 c 80.688 64.469 80.926 66.965 81.219 69.543 c 81.824 74.844
 81.438 76.578 79.461 77.477 c 77.723 78.27 77.262 78.148 75.621 76.453 
c 73.699 74.473 73.105 72.379 73.488 68.941 c 73.844 65.758 73.637 65.516
 70.723 65.746 c 68.648 65.914 l 69.328 67.863 l 69.766 69.117 69.918 70.727
 69.754 72.387 c 69.539 74.562 69.262 75.168 67.953 76.293 c 64.07 79.633
 61.844 80.465 59.867 79.32 c h
40.793 78.93 m 38.543 78.543 35.277 76.016 33.312 73.141 c 31.906 71.09
 31.75 70.531 31.781 67.754 c 31.824 64.148 32.949 59.953 34.352 58.168 
c 35.465 56.754 38.852 55.059 40.559 55.059 c 41.598 55.059 43.996 55.938
 47.668 57.668 c 50.836 59.164 51.387 60.09 52.086 65.105 c 52.602 68.805
 l 50.891 72.445 l 49.949 74.449 48.707 76.531 48.133 77.074 c 46.77 78.352
 42.988 79.309 40.793 78.93 c h
41.973 70.074 m 43.656 69.434 44.5 66.66 43.484 65.109 c 43.07 64.477 42.457
 63.957 42.125 63.957 c 40.258 63.957 38.289 68.383 39.504 69.848 c 40.156
 70.633 40.438 70.656 41.973 70.074 c h
15.539 46.508 m 14.645 45.918 13.434 44.43 12.777 43.113 c 11.711 40.98
 11.617 40.316 11.734 35.672 c 11.859 30.574 l 9.039 27.867 l 7.488 26.375
 6.219 25.074 6.219 24.969 c 6.219 24.867 5.797 22.809 5.285 20.398 c 4.77
 17.988 4.348 14.898 4.348 13.535 c 4.348 11.398 4.512 10.945 5.531 10.277
 c 7.383 9.062 9.41 9.879 11.215 12.559 c 12.41 14.34 12.758 15.438 13.023
 18.27 c 13.371 21.969 13.988 23.016 15.527 22.527 c 16.449 22.234 19.121
 19.461 20.07 17.812 c 20.555 16.977 20.672 16.992 22.727 18.199 c 24.281
 19.109 25.031 19.918 25.426 21.109 c 25.91 22.582 25.844 22.977 24.777 
24.793 c 24.121 25.914 22.785 27.527 21.809 28.387 c 20.836 29.242 20.012
 30.379 19.98 30.91 c 19.758 34.883 18.77 46.508 18.617 46.977 c 18.336 
47.848 17.324 47.691 15.539 46.508 c h
32.66 43.16 m 30.484 42.211 27.492 38.527 26.824 35.969 c 26.113 33.258
 26.156 22.082 26.891 19.18 c 27.172 18.047 27.648 17.121 27.945 17.121 
c 28.84 17.121 31.918 19.41 32.52 20.527 c 32.855 21.141 33.125 23.859 33.176
 27.141 c 33.258 31.938 33.383 32.773 34.105 33.176 c 34.738 33.531 35.09
 33.438 35.566 32.785 c 35.91 32.316 36.195 31.602 36.199 31.199 c 36.203
 30.797 36.73 28.828 37.371 26.82 c 38.012 24.816 38.559 22.18 38.582 20.969
 c 38.68 16.305 39.707 15.262 42.164 17.328 c 45.031 19.742 45.184 25.188
 42.676 35.855 c 41.676 40.113 40.676 41.57 37.738 43.059 c 35.945 43.965
 34.578 43.992 32.66 43.16 c h
48.758 42.164 m 46.258 40.863 45.797 39.918 46.43 37.398 c 47.828 31.824
 48.102 25.195 47.164 19.52 c 46.863 17.691 46.953 17.367 47.949 16.602 
c 48.566 16.125 49.52 15.73 50.066 15.727 c 51.395 15.711 54.461 19.094 
55.789 22.039 c 57.73 26.344 58.438 27.574 58.855 27.383 c 59.082 27.277
 59.324 24.027 59.391 20.16 c 59.496 13.961 59.633 12.895 60.547 11.145 
c 61.113 10.051 61.871 9.16 62.227 9.16 c 62.586 9.16 63.457 10.055 64.168
 11.148 c 64.875 12.246 65.648 13.277 65.887 13.449 c 66.121 13.617 66.234
 16.039 66.137 18.832 c 66.039 21.625 65.992 25.703 66.027 27.895 c 66.25
 41.043 66.176 41.793 64.602 42.391 c 63.109 42.957 60.754 41.805 57.918
 39.121 c 56.562 37.84 55.27 36.793 55.043 36.793 c 54.816 36.793 54.254
 38.004 53.789 39.484 c 52.441 43.812 52.184 43.949 48.758 42.164 c h
73.895 38.125 m 69.531 35.758 68.051 33.594 67.699 29.078 c 67.418 25.492
 68.945 19.121 70.605 16.945 c 71.258 16.09 71.789 15.16 71.789 14.875 c
 71.789 14.59 72.723 13.715 73.863 12.93 c 76.527 11.098 78.996 11.016 81.453
 12.684 c 84.898 15.023 85.402 16.832 85.543 27.426 c 85.602 31.52 85.5 
32.055 84.281 34.125 c 83.555 35.359 82.492 36.551 81.922 36.766 c 81.352
 36.984 80.625 37.473 80.309 37.852 c 79.621 38.684 75.27 38.867 73.895 
38.125 c h
78.539 26.801 m 80.094 23.688 79.887 21.695 77.98 21.422 c 77.137 21.301
 76.77 21.66 76.059 23.312 c 74.738 26.395 74.426 28.289 75.102 29.102 c
 76.105 30.312 77.125 29.629 78.539 26.801 c h
78.539 26.801 m f
Q Q
showpage
%%Trailer
end
%%EOF
