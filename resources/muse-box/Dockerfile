# local 测试用
#FROM linuxserver/code-server:latest
# 正式发布
FROM --platform=linux/amd64 linuxserver/code-server:4.100.2

# 设置环境变量
ENV PUID=1000 \
    PGID=1000 \
    TZ=Asia/Shanghai \
    GO_VERSION=1.24.3 \
    DEBIAN_FRONTEND=noninteractive

# 备份原始源并设置阿里云镜像源
RUN cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
    sed -i 's|http://archive.ubuntu.com/ubuntu/|https://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list && \
    sed -i 's|http://security.ubuntu.com/ubuntu/|https://mirrors.aliyun.com/ubuntu/|g' /etc/apt/sources.list && \
    sed -i 's|http://ports.ubuntu.com/ubuntu-ports/|https://mirrors.aliyun.com/ubuntu-ports/|g' /etc/apt/sources.list

# 安装基础依赖、Node.js、Go 并配置
RUN set -eux; \
    # 安装基础依赖
    apt-get update && apt-get install -y --no-install-recommends \
    sudo curl wget git vim zip unzip ca-certificates gnupg \
    python3 python3-pip supervisor nginx openssh-client tmux && \
    # 安装 Node.js 22
    mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_22.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list && \
    apt-get update && apt-get install -y --no-install-recommends nodejs && \
    # 安装 npm 包
    npm install -g npm yarn pnpm @openai/codex && \
    # 安装 Go
    ARCH=$(dpkg --print-architecture) && \
    if [ "$ARCH" = "amd64" ]; then \
        wget -qO- "https://dl.google.com/go/go${GO_VERSION}.linux-amd64.tar.gz" | tar -xz -C /usr/local; \
    elif [ "$ARCH" = "arm64" ]; then \
        wget -qO- "https://dl.google.com/go/go${GO_VERSION}.linux-arm64.tar.gz" | tar -xz -C /usr/local; \
    else \
        echo "Unsupported architecture: $ARCH" && exit 1; \
    fi && \
    echo "export PATH=\$PATH:/usr/local/go/bin" >> /etc/profile.d/go.sh && \
    echo "export PATH=\$PATH:/usr/local/go/bin" >> /etc/environment && \
    # 安装 ttyd
    if [ "$ARCH" = "amd64" ]; then \
        wget https://github.com/tsl0922/ttyd/releases/download/1.7.7/ttyd.x86_64 -O /usr/local/bin/ttyd; \
    elif [ "$ARCH" = "arm64" ]; then \
        wget https://github.com/tsl0922/ttyd/releases/download/1.7.7/ttyd.aarch64 -O /usr/local/bin/ttyd; \
    fi && \
    chmod +x /usr/local/bin/ttyd && \
    # 设置 pip 镜像源
    pip3 config set global.index-url http://pypi.sankuai.com/simple && \
    pip3 config set global.trusted-host pypi.sankuai.com && \
    # 清理 \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* && \
    # 删除不必要的文件
    rm -rf /usr/share/doc/* /usr/share/man/* /usr/share/info/* /usr/share/lintian/*

# 安装 SDKMan、Java 和 Maven
RUN set -eux; \
    # 确保 abc 用户的 home 目录存在并设置正确权限
    mkdir -p /home/<USER>
    chown -R abc:abc /home/<USER>
    sudo -u abc -H bash -c 'export HOME=/home/<USER>
    # 安装 SDKMan
    curl -s --connect-timeout 30 --max-time 300 --retry 3 --retry-delay 5 "https://get.sdkman.io" | bash && \
    source /home/<USER>/.sdkman/bin/sdkman-init.sh && \
    # 安装 Java 和 Maven
    sdk install java 8.0.452-tem < /dev/null && \
    sdk install java 11.0.27-tem < /dev/null && \
    sdk install java 17.0.15-tem < /dev/null && \
    sdk install maven 3.9.9 < /dev/null && \
    sdk default java 8.0.452-tem && \
    # 清理 SDKMan 缓存
    rm -rf /home/<USER>/.sdkman/archives/* /home/<USER>/.sdkman/tmp/*'

# 设置用户HOME目录和其他环境变量
ENV HOME=/home/<USER>
ENV USER=abc
ENV JAVA_HOME=/home/<USER>/.sdkman/candidates/java/current
ENV MAVEN_HOME=/home/<USER>/.sdkman/candidates/maven/current
ENV M2_HOME=/home/<USER>/.sdkman/candidates/maven/current
ENV GOROOT=/usr/local/go
ENV GOPATH=/workspace/go
ENV PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$GOROOT/bin:$GOPATH/bin:$PATH

# 复制配置文件
COPY nginx/nginx.conf /etc/nginx/nginx.conf
COPY nginx/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY nginx/index.html /var/www/html/index.html
COPY config/ /config/
COPY version.sh /usr/local/bin/version.sh
COPY scripts/start-terminal.sh /usr/local/bin/start-terminal.sh
COPY entrypoint.sh /entrypoint.sh
COPY --chown=abc:abc settings.xml /home/<USER>/.m2/settings.xml
COPY --chown=abc:abc codex/ /home/<USER>/.codex/
COPY --chown=abc:abc plugins/ /tmp/plugins/
COPY --chown=abc:abc id_rsa* /tmp/
COPY --chown=abc:abc known_hosts /tmp/
COPY --chown=abc:abc config/tmux.conf /home/<USER>/.tmux.conf

# 配置环境和安装扩展
RUN set -eux; \
    chmod +x /entrypoint.sh /usr/local/bin/version.sh /usr/local/bin/start-terminal.sh && \
    chown www-data:www-data /var/www/html && \
    # 配置 SSH
    sudo -u abc mkdir -p /home/<USER>/.ssh && \
    sudo -u abc chmod 700 /home/<USER>/.ssh && \
    # 如果存在 SSH 密钥文件，则复制并设置权限
    if [ -f /tmp/id_rsa ]; then \
        sudo -u abc cp /tmp/id_rsa /home/<USER>/.ssh/id_rsa && \
        sudo -u abc chmod 600 /home/<USER>/.ssh/id_rsa; \
    fi && \
    if [ -f /tmp/id_rsa.pub ]; then \
        sudo -u abc cp /tmp/id_rsa.pub /home/<USER>/.ssh/id_rsa.pub && \
        sudo -u abc chmod 644 /home/<USER>/.ssh/id_rsa.pub; \
    fi && \
    # 如果存在 known_hosts 文件，则复制并设置权限
    if [ -f /tmp/known_hosts ]; then \
        sudo -u abc cp /tmp/known_hosts /home/<USER>/.ssh/known_hosts && \
        sudo -u abc chmod 644 /home/<USER>/.ssh/known_hosts; \
    fi && \
    # 为 abc 用户配置 SSH config
    if [ -f /home/<USER>/.ssh/known_hosts ]; then \
        printf "Host *\n\tStrictHostKeyChecking yes\n\tUserKnownHostsFile ~/.ssh/known_hosts\n\tLogLevel ERROR\n\tIdentitiesOnly yes\n\tIdentityFile ~/.ssh/id_rsa\n" | sudo -u abc tee /home/<USER>/.ssh/config > /dev/null; \
    else \
        printf "Host *\n\tStrictHostKeyChecking no\n\tUserKnownHostsFile /dev/null\n\tLogLevel ERROR\n\tIdentitiesOnly yes\n\tIdentityFile ~/.ssh/id_rsa\n" | sudo -u abc tee /home/<USER>/.ssh/config > /dev/null; \
    fi && \
    sudo -u abc chmod 600 /home/<USER>/.ssh/config && \
    # 全局 SSH 配置
    mkdir -p /etc/ssh/ssh_config.d && \
    if [ -f /home/<USER>/.ssh/known_hosts ]; then \
        printf "Host *\n\tStrictHostKeyChecking yes\n\tUserKnownHostsFile /home/<USER>/.ssh/known_hosts\n\tLogLevel ERROR\n\tIdentitiesOnly yes\n" > /etc/ssh/ssh_config.d/99-ssh-config.conf; \
    else \
        printf "Host *\n\tStrictHostKeyChecking no\n\tUserKnownHostsFile /dev/null\n\tLogLevel ERROR\n\tIdentitiesOnly yes\n" > /etc/ssh/ssh_config.d/99-ssh-config.conf; \
    fi && \
    # 设置正确的权限
    chown -R abc:abc /home/<USER>/.ssh && \
    # 配置 abc 用户环境
    echo 'export HOME=/home/<USER>' >> /home/<USER>/.bashrc && \
    echo 'export USER=abc' >> /home/<USER>/.bashrc && \
    echo 'export JAVA_HOME=/home/<USER>/.sdkman/candidates/java/current' >> /home/<USER>/.bashrc && \
    echo 'export MAVEN_HOME=/home/<USER>/.sdkman/candidates/maven/current' >> /home/<USER>/.bashrc && \
    echo 'export M2_HOME=/home/<USER>/.sdkman/candidates/maven/current' >> /home/<USER>/.bashrc && \
    echo 'export GOROOT=/usr/local/go' >> /home/<USER>/.bashrc && \
    echo 'export GOPATH=/workspace/go' >> /home/<USER>/.bashrc && \
    echo 'export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$GOROOT/bin:$GOPATH/bin:$PATH' >> /home/<USER>/.bashrc && \
    echo 'source /home/<USER>/.sdkman/bin/sdkman-init.sh' >> /home/<USER>/.bashrc && \
    echo '/usr/local/bin/version.sh' >> /home/<USER>/.bashrc && \
    # 添加SSH agent启动脚本到bashrc
    echo '# SSH Agent配置' >> /home/<USER>/.bashrc && \
    echo 'if [ -f ~/.ssh/id_rsa ] && [ -z "$SSH_AUTH_SOCK" ]; then' >> /home/<USER>/.bashrc && \
    echo '    eval "$(ssh-agent -s)" > /dev/null 2>&1' >> /home/<USER>/.bashrc && \
    echo '    ssh-add ~/.ssh/id_rsa > /dev/null 2>&1' >> /home/<USER>/.bashrc && \
    echo 'fi' >> /home/<USER>/.bashrc && \
    # 设置文件权限
    chown abc:abc /home/<USER>/.bashrc && \
    # 创建必要的目录
    mkdir -p /var/log/supervisor /run/nginx /var/www/html /config/code-server /config/host /workspace /workspace/go && \
    # 设置 /config 目录权限，确保 abc 用户可以写入
    chown -R abc:abc /config && \
    chmod -R 755 /config && \
    # 确保Git配置目录权限正确，并配置Git使用SSH
    mkdir -p /home/<USER>
    chown -R abc:abc /home/<USER>
    sudo -u abc env HOME=/home/<USER>"ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" && \
    # 配置用户目录
    sudo -u abc mkdir -p /home/<USER>/.m2/repository /home/<USER>/.local/share/code-server/User && \
    sudo -u abc cp /config/code-server/settings.json /home/<USER>/.local/share/code-server/User/settings.json


# 安装 VS Code 扩展
RUN set -eux; \
    # 确保必要的目录存在并设置正确权限
    sudo -u abc mkdir -p /home/<USER>/.local/share/code-server/extensions && \
    sudo -u abc mkdir -p /home/<USER>/.local/share/code-server/User && \
    sudo -u abc mkdir -p /home/<USER>/.local/share/code-server/Machine && \
    sudo -u abc mkdir -p /home/<USER>/.config && \
    chown -R abc:abc /home/<USER>/.local && \
    chown -R abc:abc /home/<USER>/.config && \
    # 清理现有扩展缓存，强制重新下载
    rm -rf /home/<USER>/.local/share/code-server/extensions/* && \
    rm -rf /home/<USER>/.local/share/code-server/CachedExtensionVSIXs/* && \
    # 设置环境变量，确保 code-server 使用正确的配置目录
    export XDG_CONFIG_HOME=/home/<USER>/.config && \
    export XDG_DATA_HOME=/home/<USER>/.local/share && \
    # 强制安装扩展到正确的目录（使用 --force 参数）
    sudo -u abc env HOME=/home/<USER>/home/<USER>/.config XDG_DATA_HOME=/home/<USER>/.local/share /app/code-server/bin/code-server \
        --user-data-dir /home/<USER>/.local/share/code-server \
        --extensions-dir /home/<USER>/.local/share/code-server/extensions \
        --force \
        --install-extension ms-ceintl.vscode-language-pack-zh-hans \
        --install-extension ms-python.python \
        --install-extension ms-python.debugpy \
        --install-extension golang.Go \
        --install-extension Vue.volar \
        --install-extension vscjava.vscode-maven \
        --install-extension vscjava.vscode-java-pack \
        --install-extension RooVeterinaryInc.roo-cline \
        --install-extension saoudrizwan.claude-dev && \
    # 安装本地插件
    echo "检查本地插件目录..." && \
    if [ -d /tmp/plugins ]; then \
        echo "插件目录存在，列出内容:" && \
        ls -la /tmp/plugins/ && \
        if [ "$(ls -A /tmp/plugins 2>/dev/null)" ]; then \
            echo "发现本地插件，开始安装..." && \
            for plugin in /tmp/plugins/*.vsix; do \
                if [ -f "$plugin" ]; then \
                    echo "正在安装插件: $plugin" && \
                    sudo -u abc env HOME=/home/<USER>/home/<USER>/.config XDG_DATA_HOME=/home/<USER>/.local/share /app/code-server/bin/code-server \
                        --user-data-dir /home/<USER>/.local/share/code-server \
                        --extensions-dir /home/<USER>/.local/share/code-server/extensions \
                        --force \
                        --install-extension "$plugin" && \
                    echo "插件 $plugin 安装成功" || echo "插件 $plugin 安装失败"; \
                fi; \
            done; \
        else \
            echo "插件目录为空"; \
        fi; \
    else \
        echo "插件目录不存在: /tmp/plugins"; \
    fi && \
    # 验证插件安装
    echo "验证已安装的扩展..." && \
    sudo -u abc env HOME=/home/<USER>/home/<USER>/.config XDG_DATA_HOME=/home/<USER>/.local/share /app/code-server/bin/code-server \
        --user-data-dir /home/<USER>/.local/share/code-server \
        --extensions-dir /home/<USER>/.local/share/code-server/extensions \
        --list-extensions && \
    # 清理扩展缓存
    rm -rf /home/<USER>/.local/share/code-server/CachedExtensionVSIXs/* && \
    # 复制语言包配置到正确位置
    if [ -f /config/code-server/locale.json ]; then \
        sudo -u abc cp /config/code-server/locale.json /home/<USER>/.local/share/code-server/Machine/locale.json; \
    fi && \
    # 设置正确的权限
    chown -R abc:abc /home/<USER>/.local && \
    # 清理临时文件
    rm -rf /tmp/* || true

WORKDIR /workspace
# 暴露HTTPS端口
EXPOSE 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f -k https://localhost:443/health || exit 1

ENTRYPOINT ["/entrypoint.sh"]
