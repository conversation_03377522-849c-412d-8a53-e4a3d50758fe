import { useState, useEffect, useRef } from 'react';

export interface Agent {
  id: string;
  name: string;
  description: string;
  route: string;
  type?: string;
  originalId?: string;
}

interface UseAgentsReturn {
  agents: Agent[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook 用于获取可用的 agents 列表
 * 优化策略：
 * 1. 首先快速获取基础agents（fast mode）
 * 2. 然后异步获取完整数据（full mode）
 * 3. 提供无缝的用户体验
 */
export function useAgents(): UseAgentsReturn {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const fetchedRef = useRef(false);
  const fastFetchedRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 快速获取基础agents
  const fetchAgentsFast = async () => {
    if (fastFetchedRef.current) return;

    try {
      const response = await fetch('/api/agents?fast=true');
      if (!response.ok) {
        throw new Error(`Failed to fetch fast agents: ${response.statusText}`);
      }

      const data = await response.json();
      if (data.agents && Array.isArray(data.agents)) {
        setAgents(data.agents);
        setLoading(false);
        fastFetchedRef.current = true;
        console.log('Fast agents fetched successfully:', data.agents.length, 'mode:', data.mode);
      }
    } catch (err) {
      console.error('Fast fetch failed:', err);
      // 快速获取失败时，继续尝试完整获取
    }
  };

  // 完整获取agents数据
  const fetchAgentsFull = async () => {
    // 如果已经有一个请求在进行中，取消它
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      setError(null);

      const response = await fetch('/api/agents?full=true', {
        signal: abortController.signal
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch full agents: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.agents && Array.isArray(data.agents)) {
        setAgents(data.agents);
        fetchedRef.current = true;
        console.log('Full agents fetched successfully:', data.agents.length, 'mode:', data.mode);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      // 如果请求被取消，不要更新状态
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('Fetch full agents request was aborted');
        return;
      }

      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error fetching full agents:', err);

      // 如果完整获取失败，但快速获取成功了，保持现有数据
      if (!fastFetchedRef.current) {
        // 如果连快速获取都失败了，设置loading为false
        setLoading(false);
      }
    } finally {
      abortControllerRef.current = null;
    }
  };

  // 组合的获取函数
  const fetchAgents = async () => {
    // 首先快速获取基础数据
    await fetchAgentsFast();
    // 然后获取完整数据
    await fetchAgentsFull();
  };

  useEffect(() => {
    // 只在组件首次挂载时获取数据
    if (!fetchedRef.current) {
      fetchAgents();
    }

    // 清理函数：取消正在进行的请求
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []); // 空依赖数组，只在挂载时执行一次

  return {
    agents,
    loading,
    error,
    refetch: fetchAgents
  };
}

/**
 * 根据 agent ID 获取对应的路由路径
 * 使用新的参数化路由格式
 */
export function getAgentRoute(agentId: string, agents: Agent[]): string {
  const agent = agents.find((a) => a.id === agentId);
  if (agent?.route) {
    return agent.route;
  }

  // 对于新的参数化路由，直接使用 agentId
  const cleanId = agentId.replace(/Agent$/, '').toLowerCase();
  return `/${cleanId}`;
}
