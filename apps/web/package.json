{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --port 8000", "build": "NODE_ENV=test next build", "build:test": "NODE_ENV=test next build", "build:prod": "NODE_ENV=production next build", "start": "NODE_ENV=test next start --port 8000", "start:test": "NODE_ENV=test next start --port 8000", "start:prod": "NODE_ENV=production next start --port 8000", "db:push": "drizzle-kit push --force", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@mtfe/basic-auth": "^0.3.2", "@mtfe/sso-web": "^2.6.1", "@types/dockerode": "^3.3.41", "@types/react-syntax-highlighter": "^15.5.13", "@types/ws": "^8.18.1", "@workspace/agent-registry": "workspace:*", "@workspace/shared": "workspace:*", "@workspace/ui": "workspace:*", "axios": "^1.10.0", "date-fns": "^4.1.0", "dockerode": "^4.0.7", "dotenv": "^17.0.0", "drizzle-orm": "^0.44.2", "lucide-react": "^0.525.0", "moment": "^2.30.1", "mysql2": "^3.14.1", "nanoid": "^5.1.5", "next": "^15.3.4", "next-themes": "^0.4.6", "openai": "4.104.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-syntax-highlighter": "^15.6.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "ws": "^8.18.3", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "drizzle-kit": "^0.31.4", "ignore-loader": "^0.1.2", "typescript": "^5.8.3"}}