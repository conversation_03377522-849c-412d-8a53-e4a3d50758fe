// 导入已定义的类型
import { SSOWebModule } from '../sso/sso-types';

// 为@mtfe/sso-web模块定义类型
declare module '@mtfe/sso-web' {
  const autoInit: SSOWebModule['autoInit'];
  export { autoInit };
}

// 为@mtfe/basic-auth模块定义类型
declare module '@mtfe/basic-auth' {
  function header(
    path: string,
    method: string,
    clientId: string,
    secret: string
  ): {
    Authorization: string;
    [key: string]: string;
  };

  export { header };
}
