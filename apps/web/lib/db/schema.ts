import type { InferSelectModel } from 'drizzle-orm';
import { mysqlTable, varchar, text, timestamp, index } from 'drizzle-orm/mysql-core';

// Agent会话表 - 记录各个agent及uuid
export const agentSession = mysqlTable(
  'AgentSession',
  {
    id: varchar('id', { length: 255 }).primaryKey().notNull(), // 会话ID
    agentId: varchar('agentId', { length: 100 }).notNull(), // agent类型 (mario, haiku等)
    agentName: varchar('agentName', { length: 255 }).notNull(), // agent显示名称
    title: text('title').notNull(), // 会话标题
    username: varchar('username', { length: 255 }), // 用户名
    createdAt: timestamp('createdAt').notNull().defaultNow(),
    updatedAt: timestamp('updatedAt').notNull().defaultNow().onUpdateNow()
  },
  (table) => ({
    agentIdIdx: index('idx_agent_id').on(table.agentId),
    usernameIdx: index('idx_agent_session_username').on(table.username),
    createdAtIdx: index('idx_agent_session_created_at').on(table.createdAt)
  })
);

export type AgentSession = InferSelectModel<typeof agentSession>;

// Agent消息表 - 记录每个消息的详细信息
export const agentMessage = mysqlTable(
  'AgentMessage',
  {
    id: varchar('id', { length: 255 }).primaryKey().notNull(), // 消息ID
    sessionId: varchar('sessionId', { length: 255 }).notNull(), // 关联会话ID
    role: varchar('role', { length: 50 }).notNull(), // 消息角色 (user, assistant, system)
    content: text('content').notNull(), // 消息内容
    username: varchar('username', { length: 255 }), // 发送者用户名
    agentId: varchar('agentId', { length: 100 }).notNull(), // agent类型
    agentName: varchar('agentName', { length: 255 }).notNull(), // agent名称
    createdAt: timestamp('createdAt').notNull().defaultNow()
  },
  (table) => ({
    sessionIdIdx: index('idx_session_id').on(table.sessionId),
    roleIdx: index('idx_message_role').on(table.role),
    createdAtIdx: index('idx_message_created_at').on(table.createdAt),
    agentIdIdx: index('idx_message_agent_id').on(table.agentId)
  })
);

export type AgentMessage = InferSelectModel<typeof agentMessage>;
