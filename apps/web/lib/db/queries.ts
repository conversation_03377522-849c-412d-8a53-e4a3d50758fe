import { and, asc, count, desc, eq, gte } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/mysql2';
import * as mysql from 'mysql2/promise';

import { agentSession, agentMessage, type AgentSession, type AgentMessage } from './schema';
import { ChatSDKError } from '../errors';
import { dbConfig } from './config';

// 创建MySQL连接池
const connection = mysql.createPool(dbConfig);

// 创建Drizzle实例
const db = drizzle(connection);

// 初始化数据库表结构
async function initializeDatabase() {
  try {
    // 这里可以添加表创建逻辑，或者使用drizzle-kit进行迁移
    console.log('Database initialized');
  } catch (error) {
    console.error('Database initialization error:', error);
  }
}

// 在模块加载时初始化数据库
initializeDatabase();

// Agent会话相关查询函数

/**
 * 创建新的Agent会话
 */
export async function createAgentSession({
  id,
  agentId,
  agentName,
  title,
  username
}: {
  id: string;
  agentId: string;
  agentName: string;
  title: string;
  username?: string;
}) {
  try {
    console.log('💾 [DEBUG] createAgentSession - 创建agent会话:', { id, agentId, agentName, title, username });

    const result = await db.transaction(async (tx) => {
      const insertResult = await tx.insert(agentSession).values({
        id,
        agentId,
        agentName,
        title,
        username,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // 验证插入是否成功
      const [verifyResult] = await tx.select().from(agentSession).where(eq(agentSession.id, id));
      if (!verifyResult) {
        throw new Error('Failed to verify agent session insertion');
      }

      console.log('💾 [DEBUG] createAgentSession - 事务内验证成功:', verifyResult.id);
      return verifyResult;
    });

    console.log('💾 [DEBUG] createAgentSession - 事务提交成功:', id);
    return result;
  } catch (error) {
    console.error('Create agent session error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to create agent session');
  }
}

/**
 * 根据ID获取Agent会话
 */
export async function getAgentSessionById({ id }: { id: string }): Promise<AgentSession | undefined> {
  try {
    console.log('🔍 [DEBUG] getAgentSessionById - 查询session ID:', id);
    const [selectedSession] = await db.select().from(agentSession).where(eq(agentSession.id, id));
    console.log('🔍 [DEBUG] getAgentSessionById - 查询结果:', selectedSession ? '找到记录' : '未找到记录');
    return selectedSession;
  } catch (error) {
    console.error('Get agent session by id error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to get agent session by id');
  }
}

/**
 * 根据用户ID获取Agent会话列表
 */
export async function getAgentSessionsByUserId({ userId }: { userId: string }): Promise<Array<AgentSession>> {
  try {
    return await db
      .select()
      .from(agentSession)
      .where(eq(agentSession.username, userId))
      .orderBy(desc(agentSession.updatedAt));
  } catch (error) {
    console.error('Get agent sessions by user id error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to get agent sessions by user id');
  }
}

/**
 * 根据AgentId获取会话列表
 */
export async function getAgentSessionsByAgentId({ agentId }: { agentId: string }): Promise<Array<AgentSession>> {
  try {
    return await db
      .select()
      .from(agentSession)
      .where(eq(agentSession.agentId, agentId))
      .orderBy(desc(agentSession.updatedAt));
  } catch (error) {
    console.error('Get agent sessions by agent id error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to get agent sessions by agent id');
  }
}

/**
 * 更新Agent会话
 */
export async function updateAgentSession({ id, title }: { id: string; title?: string }) {
  try {
    const updateData: any = { updatedAt: new Date() };
    if (title !== undefined) updateData.title = title;

    await db.update(agentSession).set(updateData).where(eq(agentSession.id, id));
  } catch (error) {
    console.error('Update agent session error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to update agent session');
  }
}

/**
 * 删除Agent会话及其相关消息
 */
export async function deleteAgentSessionById({ id }: { id: string }) {
  try {
    const sessionToDelete = await getAgentSessionById({ id });

    await db.transaction(async (tx) => {
      // 删除相关的消息
      await tx.delete(agentMessage).where(eq(agentMessage.sessionId, id));
      // 删除会话
      await tx.delete(agentSession).where(eq(agentSession.id, id));
    });

    return sessionToDelete;
  } catch (error) {
    console.error('Delete agent session error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to delete agent session');
  }
}

// Agent消息相关查询函数

/**
 * 保存Agent消息
 */
export async function saveAgentMessage({
  id,
  sessionId,
  role,
  content,
  username,
  agentId,
  agentName
}: {
  id: string;
  sessionId: string;
  role: string;
  content: string;
  username?: string;
  agentId: string;
  agentName: string;
}) {
  try {
    console.log('💾 [DEBUG] saveAgentMessage - 保存agent消息:', { id, sessionId, role, agentId });

    const result = await db.insert(agentMessage).values({
      id,
      sessionId,
      role,
      content,
      username,
      agentId,
      agentName,
      createdAt: new Date()
    });

    // 更新会话的最后更新时间
    await db.update(agentSession).set({ updatedAt: new Date() }).where(eq(agentSession.id, sessionId));

    console.log('💾 [DEBUG] saveAgentMessage - 保存成功:', id);
    return result;
  } catch (error) {
    console.error('Save agent message error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to save agent message');
  }
}

/**
 * 更新Agent消息内容
 */
export async function updateAgentMessage({ id, content }: { id: string; content: string }) {
  try {
    console.log('💾 [DEBUG] updateAgentMessage - 更新agent消息:', { id, contentLength: content.length });

    const result = await db.update(agentMessage).set({ content }).where(eq(agentMessage.id, id));

    console.log('💾 [DEBUG] updateAgentMessage - 更新成功:', id);
    return result;
  } catch (error) {
    console.error('Update agent message error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to update agent message');
  }
}

/**
 * 保存或更新Agent消息（如果消息ID已存在则更新，否则插入新消息）
 */
export async function saveOrUpdateAgentMessage({
  id,
  sessionId,
  role,
  content,
  username,
  agentId,
  agentName
}: {
  id: string;
  sessionId: string;
  role: string;
  content: string;
  username?: string;
  agentId: string;
  agentName: string;
}) {
  try {
    console.log('💾 [DEBUG] saveOrUpdateAgentMessage - 保存或更新agent消息:', { id, sessionId, role, agentId });

    // 首先检查消息是否已存在
    const existingMessage = await getAgentMessageById({ id });

    if (existingMessage) {
      // 如果消息已存在，更新内容
      console.log('💾 [DEBUG] saveOrUpdateAgentMessage - 消息已存在，更新内容');
      await updateAgentMessage({ id, content });
    } else {
      // 如果消息不存在，插入新消息
      console.log('💾 [DEBUG] saveOrUpdateAgentMessage - 消息不存在，插入新消息');
      await db.insert(agentMessage).values({
        id,
        sessionId,
        role,
        content,
        username,
        agentId,
        agentName,
        createdAt: new Date()
      });
    }

    // 更新会话的最后更新时间
    await db.update(agentSession).set({ updatedAt: new Date() }).where(eq(agentSession.id, sessionId));

    console.log('💾 [DEBUG] saveOrUpdateAgentMessage - 操作成功:', id);
    return { id, sessionId, role, content, username, agentId, agentName };
  } catch (error) {
    console.error('Save or update agent message error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to save or update agent message');
  }
}

/**
 * 批量保存Agent消息
 */
export async function saveAgentMessages({ messages }: { messages: Array<Omit<AgentMessage, 'createdAt'>> }) {
  try {
    const messagesWithTimestamp = messages.map((msg) => ({
      ...msg,
      createdAt: new Date()
    }));

    await db.insert(agentMessage).values(messagesWithTimestamp);

    // 如果有消息，更新对应会话的最后更新时间
    if (messages.length > 0) {
      const sessionIds = [...new Set(messages.map((msg) => msg.sessionId))];
      for (const sessionId of sessionIds) {
        await db.update(agentSession).set({ updatedAt: new Date() }).where(eq(agentSession.id, sessionId));
      }
    }

    return messagesWithTimestamp;
  } catch (error) {
    console.error('Save agent messages error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to save agent messages');
  }
}

/**
 * 根据会话ID获取Agent消息列表
 */
export async function getAgentMessagesBySessionId({ sessionId }: { sessionId: string }): Promise<Array<AgentMessage>> {
  try {
    return await db
      .select()
      .from(agentMessage)
      .where(eq(agentMessage.sessionId, sessionId))
      .orderBy(asc(agentMessage.createdAt));
  } catch (error) {
    console.error('Get agent messages by session id error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to get agent messages by session id');
  }
}

/**
 * 根据ID获取Agent消息
 */
export async function getAgentMessageById({ id }: { id: string }): Promise<AgentMessage | undefined> {
  try {
    const [selectedMessage] = await db.select().from(agentMessage).where(eq(agentMessage.id, id));
    return selectedMessage;
  } catch (error) {
    console.error('Get agent message by id error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to get agent message by id');
  }
}

/**
 * 删除指定时间戳之后的Agent消息
 */
export async function deleteAgentMessagesBySessionIdAfterTimestamp({
  sessionId,
  timestamp
}: {
  sessionId: string;
  timestamp: Date;
}) {
  try {
    await db
      .delete(agentMessage)
      .where(and(eq(agentMessage.sessionId, sessionId), gte(agentMessage.createdAt, timestamp)));
  } catch (error) {
    console.error('Delete agent messages after timestamp error:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to delete agent messages after timestamp');
  }
}
