import { drizzle } from 'drizzle-orm/mysql2';
import * as mysql from 'mysql2/promise';
import { dbConfig } from './config';

/**
 * 创建MySQL连接池
 */
const connection = mysql.createPool(dbConfig);

/**
 * 导出Drizzle数据库实例
 */
export const db = drizzle(connection);

/**
 * 导出数据库配置
 */
export { dbConfig } from './config';

/**
 * 导出数据库模式
 */
export * from './schema';

/**
 * 导出查询函数
 */
export * from './queries';
