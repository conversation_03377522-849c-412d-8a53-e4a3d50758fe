// 导出所有providers
export { AuthProvider, useAuth, useSSO, useUser, type UserData } from './auth-provider';
export { Providers } from './providers';
export { AgentProvider, useAgent } from './agent-provider';

// 集中导出所有provider组件
import { ReactNode } from 'react';
import { AuthProvider } from './auth-provider';
import { AgentProvider } from './agent-provider';
import { Providers as ThemeProvider } from './providers';

/**
 * 集成所有providers的组件
 * @param children - 子组件
 */
export function AppProviders({ children }: { children: ReactNode }) {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AgentProvider>{children}</AgentProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
