'use client';

import type React from 'react';

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Send, CornerDownLeft, Plus, ArrowUp } from 'lucide-react';
import { Button } from '@workspace/ui/components/button';
import { Textarea } from '@workspace/ui/components/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
  SelectGroup
} from '@workspace/ui/components/select';
import { AgentSelector } from '@/components/agent-selector';
import type { AgentType } from '@/lib/types';
import { CopilotChat } from '@copilotkit/react-ui';
import { useCopilotChat, useCopilotAction } from '@copilotkit/react-core';
import type { ReactNode } from 'react';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import { getIntegration } from '@workspace/agent-registry/client';
import { useAgent } from '@/components/providers/agent-provider';
import { getGroupedModelOptions, DEFAULT_MODEL_ID } from '@/config/models-config';
import { useParams } from 'next/navigation';
import { CodeBlock, InlineCode } from '@/components/code-block';
import { useUserAuth } from '@/hooks/use-user-auth';

interface ChatSidebarProps {
  messages: { role: 'user' | 'assistant'; content: string }[];
  addMessage: (message: string) => void;
  selectedAgent: AgentType;
  setSelectedAgent: (agent: AgentType) => void;
  setIsAgentActive: (active: boolean) => void;
}

interface ChatInputProps {
  onSend: (message: string) => void;
  inProgress: boolean;
  setIsAgentActive: (active: boolean) => void;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
}

/**
 * 聊天输入组件
 */
function ChatInput({ onSend, inProgress, setIsAgentActive, selectedModel, setSelectedModel }: ChatInputProps) {
  const [input, setInput] = useState('');

  // 使用 useMemo 缓存 groupedModels，避免每次渲染都重新计算
  const groupedModels = useMemo(() => getGroupedModelOptions(), []);

  useEffect(() => {
    if (inProgress) {
      setIsAgentActive(true);
    } else {
      setIsAgentActive(false);
    }
  }, [inProgress, setIsAgentActive]);

  const handleSend = useCallback(
    (e?: React.MouseEvent | React.KeyboardEvent) => {
      console.log('sending message');
      e?.preventDefault();
      if (input.trim()) {
        onSend(input);
        setInput('');
        setIsAgentActive(true);
      }
    },
    [input, onSend, setIsAgentActive]
  );

  /**
   * 处理键盘事件：回车发送，Shift+回车换行
   */
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === 'Enter') {
        if (e.shiftKey) {
          // Shift + Enter: 换行，使用默认行为
          return;
        } else {
          // Enter: 发送消息
          e.preventDefault();
          handleSend(e);
        }
      }
    },
    [handleSend]
  );

  return (
    <div className='space-y-5 px-4 py-2'>
      <form className='flex flex-col gap-3'>
        <div className='relative'>
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder='输入信息...'
            className='border-muted-foreground/20 max-h-[120px] min-h-[120px] resize-none rounded-xl p-3 pb-14 pr-3'
            rows={4}
          />

          {/* 底部控制栏 - 使用flex布局确保垂直居中对齐 */}
          <div className='absolute bottom-2 left-3 right-2 flex items-center gap-2'>
            {/* 模型选择器 */}
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className='h-8 w-auto min-w-[120px] rounded-md border-0 bg-gray-100 px-3 text-xs shadow-none focus:ring-0'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {groupedModels.map((group) => (
                  <SelectGroup key={group.category}>
                    <SelectLabel>{group.categoryName}</SelectLabel>
                    {group.models.map((model) => (
                      <SelectItem key={model.value} value={model.value} className='text-xs'>
                        <div className='flex items-center gap-2'>
                          {model.iconPath && <img src={model.iconPath} alt={model.label} className='h-4 w-4 rounded' />}
                          <span>{model.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectGroup>
                ))}
              </SelectContent>
            </Select>

            {/* Agent选择器 - 添加一个包装器以匹配Select组件的样式 */}
            <div className='relative inline-flex h-9 w-auto min-w-[80px] items-center rounded-md bg-gray-100'>
              <AgentSelector />
            </div>

            {/* 占位空间，推动快捷键提示到右侧 */}
            <div className='flex-1' />

            {/* 键盘快捷键提示 */}
            <div className='text-muted-foreground mr-12 flex items-center gap-3 text-xs'>
              <div className='flex items-center gap-1'>
                <CornerDownLeft className='h-4 w-4 stroke-[3]' />
                <span>发送</span>
              </div>
              <div className='flex items-center gap-1'>
                <div className='flex items-center gap-0.5'>
                  <ArrowUp className='h-4 w-4 stroke-[3]' />
                  <Plus className='h-3 w-3' />
                  <CornerDownLeft className='h-4 w-4 stroke-[3]' />
                </div>
                <span>换行</span>
              </div>
            </div>
          </div>

          <Button
            disabled={inProgress}
            onClick={handleSend}
            className='absolute bottom-2 right-2 h-9 w-9 rounded-lg p-0'
            size='sm'>
            <Send className='h-4 w-4' />
          </Button>
        </div>
      </form>
    </div>
  );
}

/**
 * 异步获取 agent 包和初始消息的 hook
 * @param agentId - agent ID
 * @returns agent 包和初始消息
 */
function useAgentPackage(agentId: string) {
  const [agentPackage, setAgentPackage] = useState<any>(null);
  const [initialMessage, setInitialMessage] = useState('Hello! How can I help you today!');

  useEffect(() => {
    const loadAgent = async () => {
      try {
        const pkg = await getIntegration(agentId);
        setAgentPackage(pkg);
        setInitialMessage(pkg?.package?.defaultConfig?.systemPrompt || 'Hello! How can I help you today!');
      } catch (error) {
        console.error('Failed to load agent:', error);
      }
    };
    loadAgent();
  }, [agentId]);

  return { agentPackage, initialMessage };
}

export function ChatSidebar({
  messages,
  addMessage,
  selectedAgent,
  setSelectedAgent,
  setIsAgentActive
}: ChatSidebarProps) {
  const { currentAgent } = useAgent();
  const { isLoggedIn, username } = useUserAuth();
  const params = useParams();
  const sessionId = params.sessionId as string;
  const agentId = params.agentId as string;
  const { visibleMessages } = useCopilotChat();
  const [savedMessageIds, setSavedMessageIds] = useState<Set<string>>(new Set());
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastSavedContent, setLastSavedContent] = useState<Map<string, string>>(new Map());
  const [selectedModel, setSelectedModel] = useState(DEFAULT_MODEL_ID);

  // 使用 useCallback 稳定 setIsAgentActive 函数引用
  const stableSetIsAgentActive = useCallback(
    (active: boolean) => {
      setIsAgentActive(active);
    },
    [setIsAgentActive]
  );

  // 初始化时，将所有现有消息的 ID 和内容标记为已保存
  useEffect(() => {
    if (!isInitialized && visibleMessages) {
      const initialIds = new Set<string>();
      const initialContent = new Map<string, string>();

      visibleMessages.forEach((msg) => {
        if (msg instanceof TextMessage && msg.id && msg.content) {
          initialIds.add(msg.id);
          initialContent.set(msg.id, msg.content);
        }
      });

      setSavedMessageIds(initialIds);
      setLastSavedContent(initialContent);
      setIsInitialized(true);
      console.log('💾 [DEBUG] Initialized with', initialIds.size, 'existing message IDs');
    }
  }, [visibleMessages, isInitialized]);

  /**
   * 自定义Markdown渲染器，优化显示效果
   * 使用正确的HTML语义元素，避免样式和可访问性问题
   */
  const customMarkdownRenderers = useMemo(
    () => ({
      p: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <p className='copilotKitMarkdownElement mb-3 leading-relaxed text-gray-800' {...props}>
          {children}
        </p>
      ),
      code: ({ children, className, ...props }: { children?: ReactNode; className?: string; [key: string]: any }) => {
        // 处理代码块（在pre标签内的代码）
        if (className?.includes('language-')) {
          const code = typeof children === 'string' ? children : String(children);
          return (
            <CodeBlock className={className} language={className?.replace('language-', '')}>
              {code}
            </CodeBlock>
          );
        }
        // 内联代码
        return (
          <InlineCode className={className} {...props}>
            {children}
          </InlineCode>
        );
      },
      pre: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => {
        // 如果pre标签包含code子元素，直接返回children（让code组件处理）
        return <div {...props}>{children}</div>;
      },
      h1: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <h1 className='copilotKitHeading mb-4 mt-6 text-2xl font-bold text-gray-900' {...props}>
          {children}
        </h1>
      ),
      h2: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <h2 className='copilotKitHeading mb-3 mt-5 text-xl font-semibold text-gray-900' {...props}>
          {children}
        </h2>
      ),
      h3: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <h3 className='copilotKitHeading mb-2 mt-4 text-lg font-medium text-gray-900' {...props}>
          {children}
        </h3>
      ),
      ul: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <ul className='copilotKitList mb-3 ml-4 list-disc space-y-1' {...props}>
          {children}
        </ul>
      ),
      ol: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <ol className='copilotKitList mb-3 ml-4 list-decimal space-y-1' {...props}>
          {children}
        </ol>
      ),
      li: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <li className='copilotKitListItem text-gray-800' {...props}>
          {children}
        </li>
      ),
      blockquote: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <blockquote
          className='copilotKitBlockquote my-4 border-l-4 border-blue-500 bg-blue-50 p-4 italic text-gray-700'
          {...props}>
          {children}
        </blockquote>
      ),
      strong: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <strong className='copilotKitStrong font-semibold text-gray-900' {...props}>
          {children}
        </strong>
      ),
      em: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <em className='copilotKitEmphasis italic text-gray-800' {...props}>
          {children}
        </em>
      )
    }),
    []
  );

  // 保存消息到数据库的函数
  const saveMessageToDatabase = useCallback(
    async (message: string, role: 'user' | 'assistant', messageId?: string) => {
      try {
        // 确保 agentName 有正确的值
        const agentName = currentAgent?.name || agentId || 'Agent';

        // 如果是用户消息但用户未登录，则不保存 - 同时检查userData.displayName和SSO的loginName
        if (role === 'user' && !isLoggedIn) {
          console.error('用户未登录，无法保存消息');
          return;
        }

        const requestBody: any = {
          sessionId,
          role,
          content: message,
          username: role === 'user' ? username : selectedModel,
          agentId,
          agentName
        };

        // 如果提供了messageId，添加到请求体中
        if (messageId) {
          requestBody.messageId = messageId;
        }

        await fetch('/api/agent-message', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });
        console.log('💾 Message saved to database:', {
          role,
          agentName,
          messageId,
          content: message.substring(0, 50) + '...'
        });
      } catch (error) {
        console.error('Failed to save message:', error);
      }
    },
    [sessionId, agentId, currentAgent?.name, username, selectedModel]
  );

  // 监听消息变化，保存新消息和更新的消息
  useEffect(() => {
    if (!isInitialized || !visibleMessages) return;

    // 检查每条消息，如果是新的或内容有更新就保存
    visibleMessages.forEach((msg) => {
      if (msg instanceof TextMessage && msg.id && msg.content) {
        const lastContent = lastSavedContent.get(msg.id);
        const isNewMessage = !savedMessageIds.has(msg.id);
        const isContentUpdated = lastContent && lastContent !== msg.content;

        // 如果是新消息或内容有更新（针对流式输出）
        if (isNewMessage || isContentUpdated) {
          if (msg.role === Role.Assistant) {
            console.log('💾 [DEBUG] Saving assistant message with ID:', msg.id, isNewMessage ? '(new)' : '(updated)');
            // 传递消息ID，这样API可以正确地更新现有消息
            saveMessageToDatabase(msg.content, 'assistant', msg.id);
          }

          // 更新已保存的消息ID和内容
          setSavedMessageIds((prev) => new Set(prev).add(msg.id));
          setLastSavedContent((prev) => new Map(prev).set(msg.id, msg.content));
        }
      }
    });
  }, [visibleMessages, isInitialized, savedMessageIds, lastSavedContent, saveMessageToDatabase]);

  // 使用 useCallback 包装 Input 组件，避免每次渲染都创建新的组件
  const InputComponent = useCallback(
    ({ onSend, inProgress }: { onSend: (message: string) => void; inProgress: boolean }) => (
      <ChatInput
        onSend={onSend}
        inProgress={inProgress}
        setIsAgentActive={stableSetIsAgentActive}
        selectedModel={selectedModel}
        setSelectedModel={setSelectedModel}
      />
    ),
    [stableSetIsAgentActive, selectedModel, setSelectedModel]
  );

  return (
    <>
      <div className='h-full'>
        <CopilotChat
          className='h-full'
          markdownTagRenderers={customMarkdownRenderers}
          onSubmitMessage={async (message: string) => {
            // 检查用户是否已登录 - 同时检查userData.displayName和SSO的loginName
            if (!isLoggedIn) {
              console.error('用户未登录，无法发送消息');
              return;
            }
            // 用户发送消息时保存
            await saveMessageToDatabase(message, 'user');
          }}
          Input={InputComponent}
        />
      </div>
    </>
  );
}
