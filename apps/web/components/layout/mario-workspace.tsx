'use client';

import { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Textarea } from '@workspace/ui/components/textarea';
import { Card, CardContent } from '@workspace/ui/components/card';
import { ScrollArea } from '@workspace/ui/components/scroll-area';
import { Badge } from '@workspace/ui/components/badge';
import { Send, User, Bot, Gamepad2, TestTube, Palette } from 'lucide-react';
import type { AgentSession } from '@/lib/db/schema';

/**
 * Mario 聊天消息类型
 */
interface MarioChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  createdAt: string;
}

/**
 * Mario Workspace 组件属性
 */
interface MarioWorkspaceProps {
  agentId: string;
  agentName: string;
  sessionId: string;
  session: AgentSession;
  messages: MarioChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string) => Promise<void>;
  agentInstructions: string;
  suggestions: string[];
}

/**
 * Mario Workspace 组件
 * 提供 Mario 模式的专用聊天界面和功能
 */
export function MarioWorkspace({
  agentId,
  agentName,
  sessionId,
  session,
  messages,
  isLoading,
  error,
  sendMessage,
  agentInstructions,
  suggestions
}: MarioWorkspaceProps) {
  const [inputValue, setInputValue] = useState('');
  const [showInstructions, setShowInstructions] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  /**
   * 处理发送消息
   */
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const messageContent = inputValue.trim();
    setInputValue('');

    try {
      await sendMessage(messageContent);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  /**
   * 处理键盘事件
   */
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  /**
   * 处理建议点击
   */
  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    textareaRef.current?.focus();
  };

  /**
   * 格式化消息时间
   */
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className='flex h-full flex-1 flex-col'>
      {/* Mario 功能区 */}
      <div className='border-b bg-gradient-to-r from-purple-50 to-blue-50 p-4'>
        <div className='mb-3 flex items-center justify-between'>
          <h2 className='flex items-center space-x-2 text-lg font-semibold text-gray-800'>
            <Gamepad2 className='h-5 w-5 text-purple-600' />
            <span>Mario 智能助手</span>
            <Badge variant='secondary' className='bg-purple-100 text-purple-700'>
              增强模式
            </Badge>
          </h2>

          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setShowInstructions(!showInstructions)}
              className='text-xs'>
              {showInstructions ? '隐藏' : '显示'}指令
            </Button>
          </div>
        </div>

        {/* Agent 指令 */}
        {showInstructions && (
          <Card className='mb-3'>
            <CardContent className='p-3'>
              <div className='whitespace-pre-line text-sm text-gray-600'>{agentInstructions}</div>
            </CardContent>
          </Card>
        )}

        {/* 快捷建议 */}
        <div className='flex flex-wrap gap-2'>
          {suggestions.map((suggestion, index) => (
            <Button
              key={index}
              variant='outline'
              size='sm'
              onClick={() => handleSuggestionClick(suggestion)}
              className='h-8 border-purple-200 bg-white px-3 text-xs text-purple-700 hover:bg-purple-50'
              disabled={isLoading}>
              {suggestion}
            </Button>
          ))}
        </div>
      </div>

      {/* 消息区域 */}
      <div className='flex flex-1 flex-col'>
        <ScrollArea className='flex-1 p-4'>
          <div className='space-y-4'>
            {messages.length === 0 ? (
              <div className='py-12 text-center'>
                <Gamepad2 className='mx-auto mb-4 h-16 w-16 text-purple-300' />
                <h3 className='mb-2 text-lg font-medium text-gray-600'>欢迎使用 Mario 模式</h3>
                <p className='mb-4 text-gray-500'>Mario 助手已准备就绪，开始你的智能对话吧！</p>
                <div className='flex justify-center space-x-4 text-sm text-gray-400'>
                  <div className='flex items-center space-x-1'>
                    <TestTube className='h-4 w-4' />
                    <span>测试生成</span>
                  </div>
                  <div className='flex items-center space-x-1'>
                    <Palette className='h-4 w-4' />
                    <span>可视化</span>
                  </div>
                  <div className='flex items-center space-x-1'>
                    <Bot className='h-4 w-4' />
                    <span>智能分析</span>
                  </div>
                </div>
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex items-start space-x-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  {message.role === 'assistant' && (
                    <div className='flex-shrink-0'>
                      <div className='flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-blue-500'>
                        <Gamepad2 className='h-4 w-4 text-white' />
                      </div>
                    </div>
                  )}

                  <div
                    className={`max-w-[70%] rounded-lg px-4 py-3 ${
                      message.role === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'border border-gray-200 bg-white text-gray-800'
                    }`}>
                    <div className='whitespace-pre-wrap break-words'>{message.content}</div>
                    <div className={`mt-2 text-xs ${message.role === 'user' ? 'text-blue-100' : 'text-gray-500'}`}>
                      {formatTime(message.createdAt)}
                    </div>
                  </div>

                  {message.role === 'user' && (
                    <div className='flex-shrink-0'>
                      <div className='flex h-8 w-8 items-center justify-center rounded-full bg-blue-500'>
                        <User className='h-4 w-4 text-white' />
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}

            {/* 加载指示器 */}
            {isLoading && (
              <div className='flex items-start space-x-3'>
                <div className='flex-shrink-0'>
                  <div className='flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-blue-500'>
                    <Gamepad2 className='h-4 w-4 text-white' />
                  </div>
                </div>
                <div className='rounded-lg border border-gray-200 bg-white px-4 py-3'>
                  <div className='flex items-center space-x-2 text-gray-500'>
                    <div className='h-2 w-2 animate-bounce rounded-full bg-purple-400' />
                    <div
                      className='h-2 w-2 animate-bounce rounded-full bg-purple-400'
                      style={{ animationDelay: '0.1s' }}
                    />
                    <div
                      className='h-2 w-2 animate-bounce rounded-full bg-purple-400'
                      style={{ animationDelay: '0.2s' }}
                    />
                    <span className='text-sm'>Mario 正在思考...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* 输入区域 */}
        <div className='border-t bg-gray-50 p-4'>
          {error && (
            <div className='mb-3 rounded-lg border border-red-200 bg-red-50 p-3 text-sm text-red-700'>
              <strong>错误:</strong> {error}
            </div>
          )}

          <div className='flex space-x-3'>
            <div className='flex-1'>
              <Textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder='输入消息给 Mario 助手... (Enter 发送, Shift+Enter 换行)'
                className='max-h-[120px] min-h-[60px] resize-none border-gray-300 focus:border-purple-500 focus:ring-purple-500'
                disabled={isLoading}
              />
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className='h-[60px] bg-gradient-to-r from-purple-500 to-blue-500 px-6 hover:from-purple-600 hover:to-blue-600'>
              <Send className='h-4 w-4' />
            </Button>
          </div>

          <div className='mt-2 text-center text-xs text-gray-500'>
            Mario 模式 | Agent: {agentName} | Session: {sessionId.slice(0, 8)}...
          </div>
        </div>
      </div>
    </div>
  );
}
