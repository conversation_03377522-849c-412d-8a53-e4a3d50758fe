'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUserAuth } from '@/hooks/use-user-auth';
import { AuthLoadingTerminal } from '@workspace/ui/components/loading-terminal';

interface AgentClientPageProps {
  agentId: string;
  agentName: string;
}

/**
 * 客户端 Agent 页面组件
 * 自动创建新会话并重定向到带UUID的URL
 */
export default function AgentClientPage({ agentId, agentName }: AgentClientPageProps) {
  const router = useRouter();
  const { isLoggedIn, username, isLoading } = useUserAuth();
  const [isCreatingSession, setIsCreatingSession] = useState(false);

  useEffect(() => {
    const createNewSession = async () => {
      // 等待认证状态确定
      if (isLoading) {
        return;
      }

      // 检查用户是否已登录
      if (!isLoggedIn) {
        console.error('用户未登录，无法创建会话');
        // 重定向到首页，用户需要先登录
        router.replace('/');
        return;
      }

      try {
        // 设置创建会话状态
        setIsCreatingSession(true);

        // 创建新会话
        const response = await fetch('/api/agent-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            agentId,
            agentName,
            title: `${agentName}对话 - ${new Date().toLocaleString('zh-CN')}`,
            username: username || ''
          })
        });

        if (response.ok) {
          const data = await response.json();
          // 重定向到带UUID的URL
          router.replace(`/${agentId}/${data.sessionId}`);
        } else {
          console.error('Failed to create session');
          setIsCreatingSession(false);
        }
      } catch (error) {
        console.error('Error creating session:', error);
        setIsCreatingSession(false);
      }
    };

    createNewSession();
  }, [agentId, agentName, router, isLoggedIn, username, isLoading]);

  // 显示加载状态
  return <AuthLoadingTerminal agentName={agentName} isLoading={isLoading} isCreatingSession={isCreatingSession} />;
}
