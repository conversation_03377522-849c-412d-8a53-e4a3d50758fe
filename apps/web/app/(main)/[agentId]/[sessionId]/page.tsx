import React from 'react';
import { getIntegrationList, getIntegration } from '@workspace/agent-registry/server';
import { notFound } from 'next/navigation';
import { getAgentSessionById } from '@/lib/db/queries';
import AgentSessionClientPage from './client-page';

interface AgentSessionPageProps {
  params: Promise<{
    agentId: string;
    sessionId: string;
  }>;
}

/**
 * 带会话ID的Agent页面组件
 * 用于显示特定会话的历史消息
 */
export default async function AgentSessionPage({ params }: AgentSessionPageProps) {
  const { agentId, sessionId } = await params;

  // 检查 agent 是否存在
  const allIntegrations = await getIntegrationList();
  const agentExists = allIntegrations.some((integration) => integration.id === agentId);

  if (!agentExists) {
    notFound();
  }

  const agentPackage = await getIntegration(agentId);

  if (!agentPackage) {
    notFound();
  }

  // 检查会话是否存在
  const session = await getAgentSessionById({ id: sessionId });

  if (!session) {
    notFound();
  }

  // 验证会话的agentId是否匹配
  if (session.agentId !== agentId) {
    notFound();
  }

  return (
    <AgentSessionClientPage agentId={agentId} agentName={agentPackage.name} sessionId={sessionId} session={session} />
  );
}
