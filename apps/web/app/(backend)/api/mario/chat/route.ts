import { NextRequest, NextResponse } from 'next/server';
import { db, agentMessage, agentSession } from '@/lib/db';
import { eq } from 'drizzle-orm';
import { generateMessageId } from '@workspace/shared';

/**
 * <PERSON> 聊天 API 路由
 * 处理 Mario 模式下的消息发送和回复
 */
export async function POST(request: NextRequest) {
  try {
    const { message, sessionId, agentId, userId } = await request.json();

    // 验证必要字段
    if (!message || !sessionId || !agentId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // 验证会话是否存在
    const sessionResult = await db.select().from(agentSession).where(eq(agentSession.id, sessionId)).limit(1);

    if (sessionResult.length === 0) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // 保存用户消息到数据库
    await db.insert(agentMessage).values({
      id: generateMessageId(),
      sessionId,
      role: 'user',
      content: message,
      agentId,
      agentName: '<PERSON>',
      createdAt: new Date()
    });

    // 生成 <PERSON> 助手回复
    const marioResponse = await generateMarioResponse(message, agentId);

    // 保存助手回复到数据库
    await db.insert(agentMessage).values({
      id: generateMessageId(),
      sessionId,
      role: 'assistant',
      content: marioResponse,
      agentId,
      agentName: 'Mario',
      createdAt: new Date()
    });

    return NextResponse.json({
      response: marioResponse,
      success: true
    });
  } catch (error) {
    console.error('Mario chat API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * 生成 Mario 助手回复
 * 这里可以集成实际的 AI 模型或使用简单的规则回复
 */
async function generateMarioResponse(message: string, agentId: string): Promise<string> {
  // 简单的规则回复系统，后续可以替换为实际的 AI 模型
  const lowerMessage = message.toLowerCase();

  if (lowerMessage.includes('代码') || lowerMessage.includes('编程')) {
    return `我可以帮你编写代码！请告诉我你需要什么类型的代码，比如：\n\n- JavaScript/TypeScript\n- Python\n- React 组件\n- API 接口\n\n具体需要什么功能呢？`;
  }

  if (lowerMessage.includes('解释') || lowerMessage.includes('什么是')) {
    return `我很乐意为你解释概念！请提供更多细节，比如：\n\n- 你想了解哪个技术概念？\n- 是编程相关还是其他领域？\n- 需要什么深度的解释？\n\n这样我可以给出更准确的回答。`;
  }

  if (lowerMessage.includes('创意') || lowerMessage.includes('写作')) {
    return `创意写作是我的强项！我可以帮你：\n\n- 撰写文章和博客\n- 创作故事和剧本\n- 设计营销文案\n- 生成创意想法\n\n告诉我你想创作什么类型的内容吧！`;
  }

  if (lowerMessage.includes('数据') || lowerMessage.includes('分析')) {
    return `数据分析很有趣！我可以协助你：\n\n- 数据清理和处理\n- 统计分析方法\n- 可视化建议\n- 趋势解读\n\n请分享你的数据类型或分析目标，我来帮你制定方案。`;
  }

  if (lowerMessage.includes('你好') || lowerMessage.includes('hello')) {
    return `你好！我是 Mario 智能助手 🎮\n\n我可以帮助你：\n- 编写和调试代码\n- 解释技术概念\n- 创意写作和内容生成\n- 数据分析和可视化\n\n有什么我可以帮助你的吗？`;
  }

  // 默认回复
  return `感谢你的消息！作为 Mario 助手，我正在不断学习如何更好地帮助你。\n\n目前我擅长：\n- 代码编写和技术问题\n- 概念解释和学习指导\n- 创意写作和内容创作\n- 数据分析和问题解决\n\n请告诉我你需要什么帮助，我会尽力为你提供有用的回答！`;
}

/**
 * 处理 GET 请求 - 获取 Mario 助手信息
 */
export async function GET() {
  return NextResponse.json({
    name: 'Mario Chat API',
    version: '1.0.0',
    description: 'Mario 智能助手聊天接口',
    capabilities: ['智能对话', '代码生成', '概念解释', '创意写作', '数据分析']
  });
}
