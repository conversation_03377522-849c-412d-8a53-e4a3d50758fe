import { NextResponse } from 'next/server';
import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer, createServer } from 'http';
import Docker from 'dockerode';
import dockerConfig from '@/config/docker';
import socketConfig from '@/config/socket';

// 创建Docker客户端
const docker = new Docker({
  host: dockerConfig.host,
  port: dockerConfig.port,
  version: dockerConfig.version
});

// 定义一个全局变量来保存Socket.io实例
let io: SocketIOServer | null = null;

// 创建一个独立的HTTP服务器和Socket.io实例
let server: HttpServer | null = null;
let port = 0;

// 设置Socket.io事件处理
function setupSocketHandlers(socketIo: SocketIOServer) {
  socketIo.on('connection', (socket) => {
    console.log('客户端连接:', socket.id);

    // 处理终端连接
    socket.on('terminal:connect', async (containerId) => {
      console.log(`连接到容器: ${containerId}`);

      try {
        const container = docker.getContainer(containerId);

        // 检查容器状态
        const info = await container.inspect();
        if (!info.State.Running) {
          socket.emit('terminal:error', { error: '容器未运行' });
          return;
        }

        // 创建执行环境
        console.log(`为容器 ${containerId} 创建执行环境...`);
        const exec = await container.exec({
          AttachStdin: true,
          AttachStdout: true,
          AttachStderr: true,
          Tty: true,
          Cmd: ['/bin/bash']
        });
        console.log(`执行环境创建成功: ${exec.id}`);

        // 启动执行
        console.log(`启动容器 ${containerId} 的执行环境...`);
        const stream = await exec.start({
          hijack: true,
          stdin: true
        });
        console.log(`执行环境启动成功`);

        // 在连接成功后，执行entrypoint.sh脚本显示欢迎信息和版本信息
        try {
          const welcomeExec = await container.exec({
            AttachStdin: false,
            AttachStdout: true,
            AttachStderr: true,
            Tty: true,
            Cmd: ['/usr/local/bin/entrypoint.sh']
          });

          const welcomeStream = await welcomeExec.start({
            hijack: true,
            stdin: false
          });

          welcomeStream.on('data', (chunk) => {
            try {
              const output = chunk.toString();
              socket.emit('terminal:output', output);
            } catch (err) {
              console.error(`处理欢迎信息输出错误:`, err);
            }
          });

          welcomeStream.on('error', (err) => {
            console.error(`欢迎信息流错误:`, err);
          });
        } catch (welcomeError) {
          console.error('执行entrypoint.sh失败:', welcomeError);
        }

        // 处理容器输出，发送给客户端
        stream.on('data', (chunk) => {
          try {
            const output = chunk.toString();
            socket.emit('terminal:output', output);
          } catch (err) {
            console.error(`处理容器输出错误:`, err);
          }
        });

        // 处理流错误
        stream.on('error', (err) => {
          console.error(`容器流错误:`, err);
          socket.emit('terminal:error', { error: `容器流错误: ${err.message}` });
        });

        // 处理从客户端接收的输入，发送给容器
        socket.on('terminal:input', (data) => {
          try {
            stream.write(data);
          } catch (err) {
            console.error(`写入容器数据错误:`, err);
          }
        });

        // 处理连接结束
        socket.on('disconnect', () => {
          console.log(`客户端断开连接: ${socket.id}`);
          try {
            stream.end();
          } catch (err) {
            console.error(`关闭流错误:`, err);
          }
        });

        socket.on('terminal:disconnect', () => {
          console.log(`终端会话关闭: ${containerId}`);
          try {
            stream.end();
          } catch (err) {
            console.error(`关闭流错误:`, err);
          }
        });

        stream.on('end', () => {
          socket.emit('terminal:end');
        });

        // 告知客户端连接成功
        socket.emit('terminal:connected');
      } catch (error: Error | unknown) {
        console.error('连接容器终端失败:', error);
        const errorMessage = error instanceof Error ? error.message : '连接容器终端失败';
        socket.emit('terminal:error', { error: errorMessage });
      }
    });
  });
}

// 初始化独立的Socket.io服务器
interface SocketServerResult {
  success: boolean;
  port?: number;
  error?: unknown;
}

function initStandaloneSocketServer(): SocketServerResult {
  if (io) return { success: true, port };

  try {
    // 使用已导入的 createServer 函数
    server = createServer();

    // 创建一个新的Socket.io实例
    io = new SocketIOServer(server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST']
      }
    });

    // 设置Socket.io事件处理
    setupSocketHandlers(io);

    // 使用配置中的端口范围，或默认端口
    if (socketConfig.port) {
      port = socketConfig.port;
    } else {
      // 在配置的范围内随机选择端口
      const range = socketConfig.portRangeEnd - socketConfig.portRangeStart;
      port = socketConfig.portRangeStart + Math.floor(Math.random() * range);
    }

    server.listen(port, () => {
      console.log(`独立的Socket.io服务器已启动，端口: ${port}`);
    });

    return { success: true, port };
  } catch (error) {
    console.error('初始化独立的Socket.io服务器失败:', error);
    return { success: false, error };
  }
}

export async function GET() {
  const result = initStandaloneSocketServer();

  if (!result.success) {
    return NextResponse.json(
      {
        success: false,
        error: 'Socket.io服务器初始化失败',
        details: result.error
      },
      { status: 500 }
    );
  }

  // 根据环境确定使用的WebSocket协议
  // 如果主机是HTTPS或包含测试/生产域名则使用wss, 否则使用ws
  const wsProtocol =
    socketConfig.host.startsWith('https://') || socketConfig.host.includes('sankuai.com') ? 'wss' : 'ws';

  // 提取主机名，去除可能存在的协议部分
  const hostName = socketConfig.host.replace(/^https?:\/\//, '');

  return NextResponse.json({
    success: true,
    message: 'Socket.io服务器已初始化',
    port: result.port,
    wsUrl: `${wsProtocol}://${hostName}:${result.port}`
  });
}
