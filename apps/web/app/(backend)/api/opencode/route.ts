import { NextResponse } from 'next/server';
import type { NextRequest as NextRequestType } from 'next/server';
import Docker from 'dockerode';
import dockerConfig from '@/config/docker';

/**
 * 处理 POST 请求
 * 将命令转发到指定的 OpenCode Server 实例
 *
 * 请求格式:
 * ```json
 * {
 *   "containerId": "容器ID",
 *   "command": "要执行的命令",
 *   "args": { "可选参数对象" }
 * }
 * ```
 *
 * 成功响应:
 * ```json
 * {
 *   "success": true,
 *   "data": "命令执行结果"
 * }
 * ```
 *
 * 错误响应:
 * ```json
 * {
 *   "success": false,
 *   "error": "错误信息"
 * }
 * ```
 */
export async function POST(request: NextRequestType) {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证参数
    if (!body.containerId) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少容器ID参数'
        },
        { status: 400 }
      );
    }

    if (!body.command) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少命令参数'
        },
        { status: 400 }
      );
    }

    // 连接 Docker API
    const docker = new Docker({
      host: dockerConfig.host,
      port: dockerConfig.port
    });

    // 获取容器信息
    const container = docker.getContainer(body.containerId);
    const containerInfo = await container.inspect();

    // 检查容器是否在运行
    if (!containerInfo.State.Running) {
      return NextResponse.json(
        {
          success: false,
          error: '容器不在运行状态'
        },
        { status: 400 }
      );
    }

    // 获取容器 OpenCode Server 端口（远程命令 API 端口）
    const portBindings = containerInfo.HostConfig?.PortBindings || {};
    let remotePort = null;

    // 获取 OpenCode Server 的远程连接端口 (59895)
    if (portBindings['59895/tcp']?.[0]?.HostPort) {
      remotePort = Number.parseInt(portBindings['59895/tcp'][0].HostPort, 10);
    }

    if (!remotePort) {
      return NextResponse.json(
        {
          success: false,
          error: '无法获取容器远程端口'
        },
        { status: 400 }
      );
    }

    // 添加调试信息，输出完整的容器端口绑定
    console.log('容器所有端口绑定:', JSON.stringify(portBindings));
    console.log('使用的远程端口:', remotePort);

    // OpenCode Server API URL
    const apiUrl = `http://${dockerConfig.host}:${remotePort}/`;
    console.log('OpenCode Server API URL:', apiUrl);

    // 发送多种格式的请求
    let response: Response | undefined;
    let success = false;

    // 方法1: 发送带command属性的JSON对象
    try {
      console.log('尝试格式1: 发送命令对象...');
      const commandObj: { command: string; args?: Record<string, unknown> } = {
        command: body.command
      };

      // 如果有参数且不为空，添加args属性
      if (body.args && Object.keys(body.args).length > 0) {
        commandObj.args = body.args;
      }

      console.log('发送命令对象:', JSON.stringify(commandObj));

      const response1 = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(commandObj)
      });

      response = response1;
      console.log('格式1响应:', response1.status, response1.statusText);

      if (response1.ok) {
        success = true;
        console.log('格式1成功!');
      }
    } catch (error) {
      console.error('格式1请求失败:', error);
    }

    // 如果第一种方法失败，尝试方法2
    if (!success) {
      try {
        console.log('尝试格式2: 直接发送命令字符串...');

        // 直接发送命令字符串
        const response2 = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body.command)
        });

        response = response2;
        console.log('格式2响应:', response2.status, response2.statusText);

        if (response2.ok) {
          success = true;
          console.log('格式2成功!');
        }
      } catch (error) {
        console.error('格式2请求失败:', error);
      }
    }

    // 如果两种方法都失败，尝试方法3 (模拟curl的格式)
    if (!success) {
      try {
        console.log('尝试格式3: 模拟curl命令格式...');

        // 构造与curl命令相同的请求格式
        const response3 = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: `{"command":"${body.command}"}`
        });

        response = response3;
        console.log('格式3响应:', response3.status, response3.statusText);

        if (response3.ok) {
          success = true;
          console.log('格式3成功!');
        }
      } catch (error) {
        console.error('格式3请求失败:', error);

        // 如果所有方法都失败，返回错误
        return NextResponse.json(
          {
            success: false,
            error: '所有请求方法均失败',
            details: String(error)
          },
          { status: 500 }
        );
      }
    }

    // 确保response已定义
    if (!response) {
      return NextResponse.json(
        {
          success: false,
          error: '无法建立与OpenCode Server的连接'
        },
        { status: 500 }
      );
    }

    // 处理响应
    if (success && response.ok) {
      try {
        // 尝试解析为JSON
        const result = await response.json();
        console.log('成功解析响应为JSON:', result);

        return NextResponse.json({
          success: true,
          data: result
        });
      } catch {
        // 如果不是JSON格式，尝试获取文本
        try {
          const textResult = await response.text();
          console.log('响应不是JSON格式，获取为文本:', textResult);

          return NextResponse.json({
            success: true,
            data: { message: '命令已成功发送', rawResponse: textResult }
          });
        } catch {
          // 如果也无法获取文本，返回简单成功信息
          console.log('无法获取响应内容，但状态码表示成功');

          return NextResponse.json({
            success: true,
            data: { message: '命令已成功发送' }
          });
        }
      }
    } else {
      // 请求失败
      let errorDetails = '';

      try {
        errorDetails = await response.text();
      } catch {
        errorDetails = '无法获取错误详情';
      }

      return NextResponse.json(
        {
          success: false,
          error: `OpenCode Server 响应错误: ${response.status} ${response.statusText}`,
          details: errorDetails
        },
        { status: response.status || 500 }
      );
    }
  } catch (error) {
    console.error('处理 OpenCode 命令失败:', error);

    const errorMessage = error instanceof Error ? error.message : '未知错误';
    return NextResponse.json(
      {
        success: false,
        error: `处理 OpenCode 命令失败: ${errorMessage}`
      },
      { status: 500 }
    );
  }
}
