import { NextRequest, NextResponse } from 'next/server';
import { saveAgentMessage, saveOrUpdateAgentMessage, getAgentMessagesBySessionId } from '@/lib/db/queries';
import { generateMessageId } from '@workspace/shared';

/**
 * POST /api/agent-message
 * 保存Agent消息
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, role, content, username, agentId, agentName, messageId } = body;

    // 验证必填字段
    if (!sessionId || !role || !content || !agentId || !agentName) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionId, role, content, agentId, agentName' },
        { status: 400 }
      );
    }

    // 如果提供了messageId，使用它；否则生成新的ID
    const finalMessageId = messageId || generateMessageId();

    // 优先使用传入的username，如果没有且是assistant角色，则使用agentId作为fallback
    const finalUsername = username || (role === 'assistant' ? agentId : undefined);

    // 使用saveOrUpdateAgentMessage来处理插入或更新
    await saveOrUpdateAgentMessage({
      id: finalMessageId,
      sessionId,
      role,
      content,
      username: finalUsername,
      agentId,
      agentName
    });

    return NextResponse.json({
      success: true,
      messageId: finalMessageId
    });
  } catch (error) {
    console.error('Save agent message error:', error);
    return NextResponse.json({ error: 'Failed to save agent message' }, { status: 500 });
  }
}

/**
 * GET /api/agent-message?sessionId=xxx
 * 获取会话的所有消息
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({ error: 'Missing sessionId parameter' }, { status: 400 });
    }

    const messages = await getAgentMessagesBySessionId({ sessionId });

    return NextResponse.json({
      success: true,
      messages
    });
  } catch (error) {
    console.error('Get agent messages error:', error);
    return NextResponse.json({ error: 'Failed to get agent messages' }, { status: 500 });
  }
}
