{"name": "muse-studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 8000", "build": "next build", "build:test": "NODE_ENV=test next build", "build:prod": "NODE_ENV=production next build", "start": "next start --port 8000", "start:test": "NODE_ENV=test next start --port 8000", "start:prod": "NODE_ENV=production next start --port 8000", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,mdx,json,css,scss}\"", "clean": "rm -rf node_modules && rm -rf .next && find . -name node_modules -type d -prune -exec rm -rf '{}' \\; && find . -name .next -type d -prune -exec rm -rf '{}' \\; && find . -name dist -type d -prune -exec rm -rf '{}' \\;", "prepare": "simple-git-hooks", "update:deps": "taze -r -w", "update:deps:check": "taze -r", "update:deps:major": "taze -r -w major", "update:deps:minor": "taze -r -w minor", "update:deps:patch": "taze -r -w patch", "update:install": "pnpm update:deps && pnpm install --registry=http://r.npm.sankuai.com", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/openai-compatible": "^0.2.14", "@ai-sdk/react": "^1.2.12", "@ai-sdk/ui-utils": "^1.2.11", "@elastic/elasticsearch": "^9.0.2", "@hookform/resolvers": "^5.1.1", "@mtfe/basic-auth": "^0.3.2", "@mtfe/sso-web": "^2.6.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@types/dockerode": "^3.3.41", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "ai": "^4.3.16", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dockerode": "^4.0.7", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "0.509.0", "next": "15.3.2", "next-themes": "^0.4.6", "openai": "^5.8.2", "react": "^19.1.0", "react-day-picker": "9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "recharts": "^3.0.2", "remark-gfm": "^4.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/katex": "^0.16.7", "@types/markdown-it": "^14.1.2", "@types/node": "^22.15.34", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitest/ui": "^3.2.4", "codemirror": "^6.0.2", "codemirror-lang-glsl": "^0.5.0", "codemirror-lang-makefile": "^0.1.1", "codemirror-lang-terraform": "^0.1.1", "eslint": "^9.30.0", "eslint-config-next": "15.3.2", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lint-staged": "^16.1.2", "markdown-it": "^14.1.0", "markdown-it-container": "^4.0.0", "markdown-it-del": "^0.1.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-ins": "^4.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-mathjax3": "^4.3.2", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-task-checkbox": "^1.0.6", "markdown-it-task-list": "^0.1.2", "markdown-it-texmath": "^1.0.0", "marked": "^16.0.0", "marked-katex-extension": "^5.1.5", "mermaid": "^11.7.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "simple-git-hooks": "^2.13.0", "tailwindcss": "^4.1.11", "taze": "^19.1.0", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.12.4", "engines": {"node": ">=22"}, "simple-git-hooks": {"pre-commit": "npx --no -- cross-env NODE_OPTIONS=--max-old-space-size=8192 lint-staged"}, "lint-staged": {"*.mdx": ["prettier --write --no-error-on-unmatched-pattern", "eslint --fix"], "packages/ui/src/components/**/*.{js,jsx,ts,tsx}": ["echo \"Skipping shadcn components\""], "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css}": "prettier --write"}}