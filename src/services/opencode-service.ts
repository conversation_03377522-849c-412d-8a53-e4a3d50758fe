/**
 * OpenCode 服务
 * 负责与远程 OpenCode Server 的通信
 */

/**
 * 定义 OpenCode 命令类型
 * 这些命令可以发送给 OpenCode Server 来控制其行为
 */
export interface OpenCodeCommand {
    command: string;
    args?: Record<string, unknown>;
}

/**
 * 定义 OpenCode 响应类型
 */
interface OpenCodeResponse {
    success: boolean;
    data?: unknown;
    error?: string;
}

/**
 * LLM 分析请求类型
 */
interface LLMAnalysisRequest {
    interrupt_type: string;
    input_content: string;
}

/**
 * 发送命令到 OpenCode Server (直接通信方式)
 * 警告：此方法在浏览器环境中可能受到跨域限制，建议使用 sendCommandViaBackend 方法
 * @param host OpenCode Server 主机地址
 * @param port OpenCode Server 远程端口
 * @param command 要执行的命令
 * @param timeout 超时时间(毫秒)
 * @returns 命令执行结果
 */
export async function sendCommand(
    host: string,
    port: number,
    command: OpenCodeCommand,
    timeout = 5000
): Promise<OpenCodeResponse> {
    console.log(`发送命令到 OpenCode Server (${host}:${port}):`, command);

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const url = `http://${host}:${port}/`;
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(command),
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        const result = await response.json();
        console.log('命令执行结果:', result);

        return {
            success: response.ok,
            data: result,
            error: !response.ok ? result.error || '未知错误' : undefined
        };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('发送命令失败:', errorMessage);

        return {
            success: false,
            error: `发送命令失败: ${errorMessage}`
        };
    }
}

/**
 * 通过后端 API 发送命令到 OpenCode Server
 * 推荐在前端使用此方法，可以避免跨域问题并提高安全性
 * @param containerId 容器ID
 * @param command 要执行的命令
 * @param args 命令参数（可选）
 * @returns 命令执行结果
 */
export async function sendCommandViaBackend(
    containerId: string,
    command: string,
    args?: Record<string, unknown>
): Promise<OpenCodeResponse> {
    console.log(
        `通过后端API发送命令到容器 ${containerId}: ${command}`,
        args ? `参数: ${JSON.stringify(args)}` : '无参数'
    );

    try {
        const requestBody: {
            containerId: string;
            command: string;
            args?: Record<string, unknown>;
        } = { containerId, command };

        if (args && Object.keys(args).length > 0) {
            requestBody.args = args;
        }

        console.log('完整请求体:', JSON.stringify(requestBody));

        const response = await fetch('/webapi/opencode', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();
        console.log('命令执行结果:', result);

        return {
            success: result.success,
            data: result.data,
            error: !result.success ? result.error || '未知错误' : undefined
        };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('发送命令失败:', errorMessage);

        return {
            success: false,
            error: `发送命令失败: ${errorMessage}`
        };
    }
}

/**
 * 常用 OpenCode 命令
 */
export const OpenCodeCommands = {
    // 终端相关命令
    TERMINAL: {
        NEW: 'workbench.action.terminal.new',
        KILL: 'workbench.action.terminal.kill',
        CLEAR: 'workbench.action.terminal.clear',
        FOCUS: 'workbench.action.terminal.focus'
    },
    // 文件相关命令
    FILE: {
        NEW: 'workbench.action.files.newFile',
        OPEN: 'workbench.action.files.openFile',
        SAVE: 'workbench.action.files.save',
        SAVE_ALL: 'workbench.action.files.saveAll'
    },
    // 编辑器相关命令
    EDITOR: {
        CLOSE: 'workbench.action.closeActiveEditor',
        CLOSE_ALL: 'workbench.action.closeAllEditors',
        SPLIT: 'workbench.action.splitEditor'
    },
    // 视图相关命令
    VIEW: {
        EXPLORER: 'workbench.view.explorer',
        SEARCH: 'workbench.view.search',
        SCM: 'workbench.view.scm',
        DEBUG: 'workbench.view.debug'
    }
};

/**
 * 测试 OpenCode Server 连接
 * @param host OpenCode Server 主机地址
 * @param port OpenCode Server 远程端口
 * @param timeout 超时时间(毫秒)
 * @returns 连接是否成功
 */
export async function testConnection(host: string, port: number, timeout = 3000): Promise<boolean> {
    console.log(`测试 OpenCode Server 连接 (${host}:${port})...`);

    try {
        const result = await sendCommand(host, port, { command: 'workbench.action.showCommands' }, timeout);
        return result.success;
    } catch (error) {
        console.error('连接测试失败:', error);
        return false;
    }
}

/**
 * 解析 OpenCode Server 地址
 * @param webUrl OpenCode Server 的 Web URL (例如: http://*************:9001)
 * @param remotePort OpenCode Server 的远程端口 (例如: 9002)
 * @returns 解析后的主机地址和远程端口
 */
export function parseOpenCodeAddress(webUrl: string, remotePort: number): { host: string; port: number } {
    // 从 webUrl 中提取主机地址
    const host = webUrl.replace(/^https?:\/\//, '').split(':')[0];
    return {
        host,
        port: remotePort
    };
}

/**
 * 调用 LLM 分析接口
 * @param interruptType 中断类型
 * @param inputContent 输入内容
 * @returns LLM 分析结果
 */
export async function callLLMAnalysis(interruptType: string, inputContent: string): Promise<string> {
    console.log(`调用 LLM 分析接口: interrupt_type=${interruptType}, input_content=${inputContent}`);

    try {
        const requestBody: LLMAnalysisRequest = {
            interrupt_type: interruptType,
            input_content: inputContent
        };

        const response = await fetch('https://muse-app.hotel.test.sankuai.com/llm/analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();
        console.log('LLM 分析结果:', result);

        return result;
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('LLM 分析失败:', errorMessage);

        return `LLM 分析失败: ${errorMessage}`;
    }
}
