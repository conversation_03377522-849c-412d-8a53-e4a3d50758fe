'use client';

import * as React from 'react';
import Link from 'next/link';
import { Command, LifeBuoy, Send, BotMessageSquare, Store } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { NavPlaygrounds } from '@/components/nav-playground';
import { NavSecondary } from '@/components/nav-secondary';
import { NavUser } from '@/components/nav-user';
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem
} from '@/components/ui/sidebar';
import { useUser } from '@/components/providers/index';
import { UserSkeleton } from '@/components/ui/user-skeleton';

const data = {
    navPlaygrounds: [
        {
            name: 'Chat',
            url: '/chat',
            icon: BotMessageSquare
        },
        {
            name: 'MCP Market',
            url: '/mcp',
            icon: Store // 导入 Store 图标
        }
    ],
    navSecondary: [
        {
            title: 'Support',
            url: '#',
            icon: LifeBuoy
        },
        {
            title: 'Feedback',
            url: '#',
            icon: Send
        }
    ]
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const pathname = usePathname();
    const isRoot = pathname === '/';
    const { userData, isLoading } = useUser();

    return (
        <Sidebar variant='inset' collapsible='icon' {...props}>
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size='lg' asChild isActive={isRoot}>
                            <Link href='/'>
                                <div className='bg-sidebar-primary text-sidebar-primary-foreground relative flex aspect-square size-8 shrink-0 items-center justify-center overflow-hidden rounded-lg'>
                                    <Command className='z-10 size-4' />
                                    <div className='animate-shimmer absolute inset-0 bg-gradient-to-r' />
                                </div>
                                <div className='grid flex-1 text-left text-sm leading-tight'>
                                    <span className='truncate font-medium'>Muse</span>
                                    <span className='truncate text-xs'>Studio</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>
            <SidebarContent>
                <NavPlaygrounds playgrounds={data.navPlaygrounds} />
                <NavSecondary items={data.navSecondary} className='mt-auto' />
            </SidebarContent>
            <SidebarFooter>{isLoading ? <UserSkeleton /> : <NavUser user={userData} />}</SidebarFooter>
        </Sidebar>
    );
}
