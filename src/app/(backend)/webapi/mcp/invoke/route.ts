import { NextResponse } from 'next/server';
import { mcpManager } from '@/lib/mcp-manager';

export async function POST(req: Request) {
    try {
        const { serverId, toolName, params } = await req.json();

        if (!serverId || !toolName) {
            return NextResponse.json({ error: 'Missing required parameters: serverId or toolName' }, { status: 400 });
        }

        const result = await mcpManager.invokeTool(serverId, toolName, params || {});
        return NextResponse.json({ result });
    } catch (error) {
        console.error('MCP invoke error:', error);
        return NextResponse.json(
            {
                error: 'Failed to invoke MCP tool',
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    }
}
