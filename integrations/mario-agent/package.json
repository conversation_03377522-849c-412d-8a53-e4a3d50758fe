{"name": "@workspace/mario-agent", "version": "0.0.1", "type": "module", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js", "require": "./dist/server.cjs"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@workspace/shared": "workspace:*", "@workspace/ui": "workspace:*", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dockerode": "^4.0.7", "lucide-react": "^0.525.0", "openai": "4.104.0", "react": "^19.1.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@types/dockerode": "^3.3.41", "@types/react": "^19.1.8", "@types/react-syntax-highlighter": "^15.5.13", "@types/ws": "^8.18.1", "@workspace/typescript-config": "workspace:*", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^19.1.0"}}