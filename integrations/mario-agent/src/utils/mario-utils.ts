import type { MarioMessage, ContainerInfo, MarioCase, HealthCheckResult, AsyncRetryFunction } from '../types/mario.js';
import { generateMessageId } from '@workspace/shared';

/**
 * 生成唯一ID - 使用统一的nanoid
 */
export function generateId(): string {
  return generateMessageId();
}

/**
 * 格式化时间戳
 */
export function formatTimestamp(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).format(date);
}

/**
 * 格式化日期时间
 */
export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).format(date);
}

/**
 * 创建Mario消息
 */
export function createMarioMessage(
  role: 'user' | 'assistant',
  content: string,
  options: {
    type?: 'text' | 'error' | 'system' | 'interrupt';
    metadata?: Record<string, any>;
  } = {}
): MarioMessage {
  return {
    id: generateId(),
    role,
    content,
    timestamp: new Date(),
    type: options.type || 'text',
    metadata: options.metadata
  };
}

/**
 * 创建系统消息
 */
export function createSystemMessage(content: string, metadata?: Record<string, any>): MarioMessage {
  return createMarioMessage('assistant', content, {
    type: 'system',
    metadata
  });
}

/**
 * 创建错误消息
 */
export function createErrorMessage(content: string, metadata?: Record<string, any>): MarioMessage {
  return createMarioMessage('assistant', content, {
    type: 'error',
    metadata
  });
}

/**
 * 验证端口号
 */
export function isValidPort(port: number): boolean {
  return Number.isInteger(port) && port >= 1 && port <= 65535;
}

/**
 * 验证容器ID
 */
export function isValidContainerId(id: string): boolean {
  return typeof id === 'string' && id.length >= 12 && /^[a-f0-9]+$/.test(id);
}

/**
 * 截断容器ID显示
 */
export function truncateContainerId(id: string, length: number = 12): string {
  return id.slice(0, length);
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: AsyncRetryFunction<T>,
  options: {
    attempts?: number;
    delay?: number;
    backoff?: boolean;
    onRetry?: (attempt: number, error: Error) => void;
  } = {}
): Promise<T> {
  const { attempts = 3, delay: delayMs = 1000, backoff = true, onRetry } = options;

  let lastError: Error;

  for (let attempt = 1; attempt <= attempts; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error as Error;

      if (attempt === attempts) {
        throw lastError;
      }

      onRetry?.(attempt, lastError);

      const currentDelay = backoff ? delayMs * Math.pow(2, attempt - 1) : delayMs;
      await delay(currentDelay);
    }
  }

  throw lastError!;
}

/**
 * 超时包装函数
 */
export function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    })
  ]);
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as T;
  }

  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }

  return obj;
}

/**
 * 合并对象
 */
export function mergeDeep<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        mergeDeep(target[key] as Record<string, any>, source[key] as Record<string, any>);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }

  return mergeDeep(target, ...sources);
}

/**
 * 检查是否为对象
 */
function isObject(item: any): item is Record<string, any> {
  return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T = any>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str);
  } catch {
    return defaultValue;
  }
}

/**
 * 安全的JSON字符串化
 */
export function safeJsonStringify(obj: any, space?: number): string {
  try {
    return JSON.stringify(obj, null, space);
  } catch {
    return '{}';
  }
}

/**
 * 检查URL是否有效
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 构建WebSocket URL
 */
export function buildWebSocketUrl(baseUrl: string, path: string = ''): string {
  const url = new URL(baseUrl);
  url.protocol = url.protocol === 'https:' ? 'wss:' : 'ws:';
  if (path) {
    url.pathname = path;
  }
  return url.toString();
}

/**
 * 构建HTTP URL
 */
export function buildHttpUrl(host: string, port: number, path: string = '', protocol: string = 'http'): string {
  return `${protocol}://${host}:${port}${path}`;
}

/**
 * 提取错误消息
 */
export function extractErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message);
  }

  return '未知错误';
}

/**
 * 生成随机端口
 */
export function generateRandomPort(min: number = 3000, max: number = 9000): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 检查端口是否可用（简单检查）
 */
export async function isPortAvailable(port: number, host: string = 'localhost'): Promise<boolean> {
  try {
    const response = await fetch(`http://${host}:${port}`, {
      method: 'HEAD',
      mode: 'no-cors'
    });
    return false; // 如果能连接，说明端口被占用
  } catch {
    return true; // 连接失败，说明端口可用
  }
}

/**
 * 清理HTML标签
 */
export function stripHtmlTags(html: string): string {
  return html.replace(/<[^>]*>/g, '');
}

/**
 * 转义HTML字符
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // 降级方案
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 下载文本文件
 */
export function downloadTextFile(content: string, filename: string, mimeType: string = 'text/plain'): void {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
}

/**
 * 获取MIME类型
 */
export function getMimeType(filename: string): string {
  const ext = getFileExtension(filename).toLowerCase();
  const mimeTypes: Record<string, string> = {
    txt: 'text/plain',
    html: 'text/html',
    css: 'text/css',
    js: 'application/javascript',
    json: 'application/json',
    xml: 'application/xml',
    pdf: 'application/pdf',
    zip: 'application/zip',
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    gif: 'image/gif',
    svg: 'image/svg+xml'
  };

  return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * 格式化容器状态
 */
export function formatContainerStatus(container: ContainerInfo): string {
  const statusMap: Record<string, string> = {
    creating: '创建中',
    running: '运行中',
    stopped: '已停止',
    error: '错误'
  };

  return statusMap[container.status] || container.status;
}

/**
 * 格式化健康状态
 */
export function formatHealthStatus(status: string): string {
  const statusMap = {
    healthy: '健康',
    unhealthy: '不健康',
    starting: '启动中',
    unknown: '未知'
  };

  return statusMap[status as keyof typeof statusMap] || status;
}

/**
 * 计算相对时间
 */
export function getRelativeTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return '刚刚';
  }
}

/**
 * 验证Mario用例
 */
export function validateMarioCase(marioCase: Partial<MarioCase>): string[] {
  const errors: string[] = [];

  if (!marioCase.title || marioCase.title.trim().length === 0) {
    errors.push('用例标题不能为空');
  }

  if (!marioCase.description || marioCase.description.trim().length === 0) {
    errors.push('用例描述不能为空');
  }

  if (!marioCase.steps || marioCase.steps.length === 0) {
    errors.push('用例步骤不能为空');
  }

  if (!marioCase.expectedResult || (marioCase.expectedResult && marioCase.expectedResult.trim().length === 0)) {
    errors.push('期望结果不能为空');
  }

  return errors;
}

/**
 * 生成容器名称
 */
export function generateContainerName(prefix: string = 'mario'): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 5);
  return `${prefix}-${timestamp}-${random}`;
}

/**
 * 解析Docker镜像标签
 */
export function parseDockerImage(image: string): { name: string; tag: string } {
  const parts = image.split(':');
  return {
    name: parts[0] || '',
    tag: parts[1] || 'latest'
  };
}

/**
 * 格式化Docker镜像
 */
export function formatDockerImage(name: string, tag: string = 'latest'): string {
  return `${name}:${tag}`;
}
