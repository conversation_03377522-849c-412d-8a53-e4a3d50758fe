'use client';

import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import { Lightbulb, Maximize, Minimize } from 'lucide-react';
import { AnimatedGradientText } from '@workspace/ui/components/magicui/animated-gradient-text';
import { BaseWorkspaceProps } from '@workspace/shared';
import { createAgent } from './agent.js';
import { useRef, useState, useEffect } from 'react';

/**
 * Agent 工作区组件
 * 提供简化的界面，只包含 Canvas Header
 */
export function Workspace({ setIsAgentActive, isAgentActive }: BaseWorkspaceProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 创建 Agent 实例
  const agent = useRef(createAgent());

  /**
   * 切换全屏模式
   */
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  /**
   * 监听ESC键退出全屏
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullscreen]);

  return (
    <div className={`flex h-full flex-col ${isFullscreen ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900' : ''}`}>
      {/* Canvas Header */}
      <div className='h-12 w-full border-b border-gray-200 dark:border-gray-700'>
        <div className='flex h-full items-center justify-between px-6'>
          <div className='flex items-center gap-4'>
            <Button
              variant='outline'
              size='sm'
              onClick={toggleFullscreen}
              className='h-8 w-8 border-gray-300 p-0 transition-colors hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-800'
              title={isFullscreen ? '退出全屏' : '全屏'}>
              {isFullscreen ? <Minimize className='h-4 w-4' /> : <Maximize className='h-4 w-4' />}
            </Button>

            {isAgentActive && (
              <Badge variant='default' className='h-8 gap-2'>
                <Lightbulb className='h-3 w-3' />
                <AnimatedGradientText>Agent Contributing</AnimatedGradientText>
              </Badge>
            )}
          </div>
        </div>
      </div>
      
      {/* Canvas Content */}
      <div className='flex-1'>
        <div className='flex h-full w-full items-center justify-center'>
          <div className='text-center'>
            <h2 className='mb-4 text-2xl font-bold text-gray-800 dark:text-gray-200'>Mario Agent Canvas</h2>
            <p className='text-gray-600 dark:text-gray-400'>Canvas content area for mario creation and editing</p>
          </div>
        </div>
      </div>
    </div>
  );
}
