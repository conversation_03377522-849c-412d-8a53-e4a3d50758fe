{"name": "@workspace/haiku-agent", "version": "0.0.1", "type": "module", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js", "require": "./dist/server.cjs"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@workspace/shared": "workspace:*", "@workspace/ui": "workspace:*", "lucide-react": "^0.525.0", "openai": "4.104.0", "react": "^19.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/react": "^19.1.8", "@workspace/typescript-config": "workspace:*", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^19.1.0"}}