#!/usr/bin/env node

/**
 * Agent API性能测试脚本
 * 测试快速模式和完整模式的响应时间
 */

const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:8000';
const TEST_ROUNDS = 5;

// 发送HTTP请求的工具函数
function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = http.get(`${BASE_URL}${path}`, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const jsonData = JSON.parse(data);
          resolve({
            responseTime,
            data: jsonData,
            statusCode: res.statusCode
          });
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
  });
}

// 运行性能测试
async function runPerformanceTest() {
  console.log('🚀 开始Agent API性能测试...\n');
  
  const fastModeTimes = [];
  const fullModeTimes = [];
  
  // 测试快速模式
  console.log('📊 测试快速模式 (/api/agents?fast=true)');
  for (let i = 0; i < TEST_ROUNDS; i++) {
    try {
      const result = await makeRequest('/api/agents?fast=true');
      fastModeTimes.push(result.responseTime);
      console.log(`  第${i + 1}轮: ${result.responseTime}ms, agents数量: ${result.data.agents?.length || 0}, 模式: ${result.data.mode}`);
    } catch (error) {
      console.error(`  第${i + 1}轮失败: ${error.message}`);
    }
  }
  
  console.log('');
  
  // 测试完整模式
  console.log('📊 测试完整模式 (/api/agents?full=true)');
  for (let i = 0; i < TEST_ROUNDS; i++) {
    try {
      const result = await makeRequest('/api/agents?full=true');
      fullModeTimes.push(result.responseTime);
      console.log(`  第${i + 1}轮: ${result.responseTime}ms, agents数量: ${result.data.agents?.length || 0}, 模式: ${result.data.mode}`);
    } catch (error) {
      console.error(`  第${i + 1}轮失败: ${error.message}`);
    }
  }
  
  console.log('');
  
  // 计算统计数据
  const calculateStats = (times) => {
    if (times.length === 0) return { avg: 0, min: 0, max: 0 };
    
    const avg = times.reduce((sum, time) => sum + time, 0) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    
    return { avg: Math.round(avg), min, max };
  };
  
  const fastStats = calculateStats(fastModeTimes);
  const fullStats = calculateStats(fullModeTimes);
  
  // 输出结果
  console.log('📈 性能测试结果:');
  console.log('================');
  console.log(`快速模式 - 平均: ${fastStats.avg}ms, 最小: ${fastStats.min}ms, 最大: ${fastStats.max}ms`);
  console.log(`完整模式 - 平均: ${fullStats.avg}ms, 最小: ${fullStats.min}ms, 最大: ${fullStats.max}ms`);
  
  if (fastStats.avg > 0 && fullStats.avg > 0) {
    const improvement = Math.round(((fullStats.avg - fastStats.avg) / fullStats.avg) * 100);
    console.log(`\n⚡ 快速模式比完整模式快 ${improvement}%`);
  }
  
  // 测试数据一致性
  console.log('\n🔍 测试数据一致性...');
  try {
    const fastResult = await makeRequest('/api/agents?fast=true');
    const fullResult = await makeRequest('/api/agents?full=true');
    
    const fastAgents = fastResult.data.agents || [];
    const fullAgents = fullResult.data.agents || [];
    
    console.log(`快速模式agents: ${fastAgents.map(a => a.id).join(', ')}`);
    console.log(`完整模式agents: ${fullAgents.map(a => a.id).join(', ')}`);
    
    // 检查基础agents是否都存在
    const baseAgentIds = ['mario', 'haiku'];
    const fastHasBase = baseAgentIds.every(id => fastAgents.some(a => a.id === id));
    const fullHasBase = baseAgentIds.every(id => fullAgents.some(a => a.id === id));
    
    console.log(`\n✅ 基础agents完整性:`);
    console.log(`  快速模式: ${fastHasBase ? '✓' : '✗'} (${fastHasBase ? '包含所有基础agents' : '缺少基础agents'})`);
    console.log(`  完整模式: ${fullHasBase ? '✓' : '✗'} (${fullHasBase ? '包含所有基础agents' : '缺少基础agents'})`);
    
  } catch (error) {
    console.error(`数据一致性测试失败: ${error.message}`);
  }
  
  console.log('\n🎯 优化建议:');
  if (fastStats.avg < 50) {
    console.log('✅ 快速模式响应时间优秀 (<50ms)');
  } else if (fastStats.avg < 100) {
    console.log('⚠️  快速模式响应时间良好 (50-100ms)');
  } else {
    console.log('❌ 快速模式响应时间需要优化 (>100ms)');
  }
  
  if (fullStats.avg < 200) {
    console.log('✅ 完整模式响应时间优秀 (<200ms)');
  } else if (fullStats.avg < 500) {
    console.log('⚠️  完整模式响应时间良好 (200-500ms)');
  } else {
    console.log('❌ 完整模式响应时间需要优化 (>500ms)');
  }
}

// 主函数
async function main() {
  try {
    await runPerformanceTest();
  } catch (error) {
    console.error('测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { makeRequest, runPerformanceTest };
