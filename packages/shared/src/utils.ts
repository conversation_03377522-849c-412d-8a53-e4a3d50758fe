// 工具函数集合

import { nanoid } from 'nanoid';

// 统一的消息ID生成函数，使用nanoid
export function generateMessageId(): string {
  return nanoid();
}

// 保持向后兼容的generateId函数
export function generateId(): string {
  return generateMessageId();
}

// 延迟函数
export const delay = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

// 安全的JSON解析
export const safeJsonParse = <T = any>(jsonString: string, defaultValue: T): T => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('JSON parse error:', error);
    return defaultValue;
  }
};

// 格式化错误信息
export const formatError = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'Unknown error occurred';
};

// 深度合并对象
export const deepMerge = <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
  const result = { ...target };

  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || ({} as any), source[key] as any);
      } else {
        result[key] = source[key] as any;
      }
    }
  }

  return result;
};

// 验证integration ID格式
export const validateIntegrationId = (id: string): boolean => {
  // integration ID 应该是 kebab-case 格式
  const pattern = /^[a-z]+(?:-[a-z]+)*$/;
  return pattern.test(id);
};

// 创建integration命名空间
export const createIntegrationNamespace = (id: string): string => {
  if (!validateIntegrationId(id)) {
    throw new Error(`Invalid integration ID: ${id}. Must be kebab-case format.`);
  }
  return `@integrations/${id}`;
};
