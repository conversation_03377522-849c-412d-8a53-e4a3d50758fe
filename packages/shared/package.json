{"name": "@workspace/shared", "version": "0.0.1", "type": "module", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@ag-ui/client": "^0.0.30", "@ai-sdk/openai": "^1.3.22", "nanoid": "^5.1.5", "react": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.8", "@workspace/typescript-config": "workspace:*", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^19.1.0"}}