'use client';

import { cn } from '@workspace/ui/lib/utils';
import { motion } from 'motion/react';
import { useEffect, useRef, useState } from 'react';

interface AnimatedSpanProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
}

export const AnimatedSpan = ({ children, delay = 0, className }: AnimatedSpanProps) => (
  <motion.div
    initial={{ opacity: 0, y: -5 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3, delay: delay / 1000 }}
    className={cn('grid text-sm font-normal tracking-tight', className)}
  >
    {children}
  </motion.div>
);

interface TypingAnimationProps {
  children: string;
  className?: string;
  duration?: number;
  delay?: number;
}

export const TypingAnimation = ({
  children,
  className,
  duration = 60,
  delay = 0
}: TypingAnimationProps) => {
  const [displayedText, setDisplayedText] = useState<string>('');
  const [started, setStarted] = useState(false);

  useEffect(() => {
    const startTimeout = setTimeout(() => {
      setStarted(true);
    }, delay);
    return () => clearTimeout(startTimeout);
  }, [delay]);

  useEffect(() => {
    if (!started) return;

    // 确保 children 是字符串类型
    const text = typeof children === 'string' ? children : String(children);
    
    let i = 0;
    const typingEffect = setInterval(() => {
      if (i < text.length) {
        setDisplayedText(text.substring(0, i + 1));
        i++;
      } else {
        clearInterval(typingEffect);
      }
    }, duration);

    return () => {
      clearInterval(typingEffect);
    };
  }, [children, duration, started]);

  return (
    <span className={cn('text-sm font-normal tracking-tight', className)}>
      {displayedText}
    </span>
  );
};

interface LoadingTerminalProps {
  className?: string;
  children: React.ReactNode;
}

export const LoadingTerminal = ({ children, className }: LoadingTerminalProps) => {
  return (
    <div className="flex h-screen items-center justify-center">
      <div
        className={cn(
          'border-border bg-background z-0 h-full max-h-[400px] w-full max-w-lg rounded-xl border',
          className
        )}
      >
        <div className='border-border flex flex-col gap-y-2 border-b p-4'>
          <div className='flex flex-row gap-x-2'>
            <div className='h-2 w-2 rounded-full bg-red-500'></div>
            <div className='h-2 w-2 rounded-full bg-yellow-500'></div>
            <div className='h-2 w-2 rounded-full bg-green-500'></div>
          </div>
        </div>
        <pre className='p-4'>
          <code className='grid gap-y-1 overflow-auto'>{children}</code>
        </pre>
      </div>
    </div>
  );
};

interface AuthLoadingTerminalProps {
  agentName?: string;
  isLoading: boolean;
  isCreatingSession: boolean;
}

export const AuthLoadingTerminal = ({ 
  agentName, 
  isLoading, 
  isCreatingSession 
}: AuthLoadingTerminalProps) => {
  return (
    <LoadingTerminal>
      <TypingAnimation>{"> 启动智能助手系统..."}</TypingAnimation>

      <AnimatedSpan delay={1000} className="text-green-500">
        <span>✔ 初始化系统环境</span>
      </AnimatedSpan>

      <AnimatedSpan delay={1500} className="text-green-500">
        <span>✔ 加载安全模块</span>
      </AnimatedSpan>

      {isLoading && (
        <>
          <AnimatedSpan delay={2000} className="text-yellow-500">
            <span>⏳ 正在验证用户身份...</span>
          </AnimatedSpan>
        </>
      )}

      {!isLoading && (
        <>
          <AnimatedSpan delay={2000} className="text-green-500">
            <span>✔ 用户身份验证成功</span>
          </AnimatedSpan>

          <AnimatedSpan delay={2500} className="text-green-500">
            <span>✔ 权限检查通过</span>
          </AnimatedSpan>
        </>
      )}

      {isCreatingSession && agentName && (
        <>
          <AnimatedSpan delay={3000} className="text-yellow-500">
            <span>⏳ 正在创建 {agentName} 新会话...</span>
          </AnimatedSpan>

          <AnimatedSpan delay={3500} className="text-blue-500">
            <span>ℹ 配置会话参数</span>
          </AnimatedSpan>

          <AnimatedSpan delay={4000} className="text-blue-500">
            <span>ℹ 初始化对话上下文</span>
          </AnimatedSpan>
        </>
      )}

      {!isLoading && !isCreatingSession && (
        <>
          <AnimatedSpan delay={3000} className="text-green-500">
            <span>✔ 会话创建成功</span>
          </AnimatedSpan>

          <TypingAnimation delay={3500} className="text-muted-foreground">
            正在跳转到对话界面...
          </TypingAnimation>
        </>
      )}
    </LoadingTerminal>
  );
};

interface SessionLoadingTerminalProps {
  sessionTitle: string;
}

export const SessionLoadingTerminal = ({ sessionTitle }: SessionLoadingTerminalProps) => {
  return (
    <LoadingTerminal>
      <TypingAnimation>{`> 加载会话: ${sessionTitle}`}</TypingAnimation>

      <AnimatedSpan delay={1000} className="text-green-500">
        <span>✔ 连接到会话服务器</span>
      </AnimatedSpan>

      <AnimatedSpan delay={1500} className="text-green-500">
        <span>✔ 验证会话权限</span>
      </AnimatedSpan>

      <AnimatedSpan delay={2000} className="text-yellow-500">
        <span>⏳ 正在加载历史消息...</span>
      </AnimatedSpan>

      <AnimatedSpan delay={2500} className="text-blue-500">
        <span>ℹ 恢复对话上下文</span>
      </AnimatedSpan>

      <AnimatedSpan delay={3000} className="text-blue-500">
        <span>ℹ 初始化 CopilotKit 组件</span>
      </AnimatedSpan>

      <AnimatedSpan delay={3500} className="text-green-500">
        <span>✔ 会话加载完成</span>
      </AnimatedSpan>

      <TypingAnimation delay={4000} className="text-muted-foreground">
        准备就绪，可以开始对话了！
      </TypingAnimation>
    </LoadingTerminal>
  );
};
