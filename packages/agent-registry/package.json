{"name": "@workspace/agent-registry", "version": "0.0.1", "type": "module", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js", "require": "./dist/server.cjs"}, "./client": {"types": "./dist/client.d.ts", "import": "./dist/client.js", "require": "./dist/client.cjs"}, "./registry": {"types": "./dist/registry.d.ts", "import": "./dist/registry.js", "require": "./dist/registry.cjs"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@workspace/haiku-agent": "workspace:*", "@workspace/mario-agent": "workspace:*", "@workspace/shared": "workspace:*", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0"}, "devDependencies": {"@workspace/typescript-config": "workspace:*", "tsup": "^8.5.0", "typescript": "^5.8.3"}}