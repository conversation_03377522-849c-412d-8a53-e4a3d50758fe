import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts', 'src/registry.ts', 'src/server.ts', 'src/client.ts'],
  format: ['esm', 'cjs'],
  dts: true,
  clean: true,
  splitting: false,
  sourcemap: true,
  external: [
    'react',
    'react-dom',
    // 工作区包
    '@workspace/shared',
    '@workspace/haiku-agent',
    '@workspace/mario-agent',
    // 原生模块和包含原生依赖的包
    'dockerode',
    'ssh2',
    '@xterm/xterm',
    '@xterm/addon-fit',
    '@xterm/addon-search',
    '@xterm/addon-web-links',
    '@xterm/addon-webgl',
    // ag-ui 相关包
    '@ag-ui/client',
    '@ag-ui/core',
    '@ag-ui/encoder',
    '@ag-ui/mastra',
    '@ag-ui/proto'
  ]
});
